package com.yunqu.yc.quality.dao;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.util.*;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.*;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yunqu.yc.quality.base.AppDaoContext;
import com.yunqu.yc.quality.base.CommonLogger;
import com.yunqu.yc.quality.base.Constants;
import com.yunqu.yc.quality.dao.sql.QcResultSql;
import com.yunqu.yc.quality.dao.sql.QcStatSql;
import com.yunqu.yc.quality.enums.ItemEnum;
import com.yunqu.yc.quality.utils.StatQuery;
import com.yunqu.yc.quality.utils.StringUtil;
import org.jetbrains.annotations.NotNull;

@WebObject(name="QcResultDao")
public class QcResultDao extends AppDaoContext {
	private Logger logger = CommonLogger.logger;
	//质检结果
	@InfAuthCheck(resId ={"cc-qc-rwzx","cc-qc-zjjg","cc-qc-yxlycx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="resultList", type=Types.LIST)
	public JSONObject resultList(){
		UserModel user = UserUtil.getUser(request);
		logger.info("[QcResultDao]param:" + param);
		EasySQL sql = QcResultSql.getRgResultListSql(user, param, request,getQuery());
		logger.info(sql.getSQL()+":"+JSON.toJSONString(sql.getParams()));
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	//质检结果项明细
	@WebControl(name="itemList", type=Types.LIST)
	public JSONObject itemList(){
		EasySQL sql = this.getEasySQL("select t1.ITEM_NAME,t1.SCORE,t1.ITEM_DESC,t2.STD_SCORE from ");
		sql.append(getTableName("CC_QC_RESULT_ITEM t1,"));
		sql.append(getTableName("CC_QC_ITEM t2"));
		sql.append(" where 1=1 and t1.ITEM_ID = t2.ITEM_ID");
		sql.append(param.getString("qcResultId")," and t1.QC_RESULT_ID = ?", false);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	//质检结果
	@WebControl(name="template", type=Types.TEMPLATE)
	public JSONObject template(){
		String pk = param.getString("classId");
		if(StringUtils.isBlank(pk)){
			pk = param.getString("pk");
		}
		String qcResultId = param.getString("record.QC_RESULT_ID");
		String objId = param.getString("objId");
		UserModel user = getUser();
		EasySQL sql = null;
		if(CommonUtil.isNotBlank(qcResultId)) {
			sql = this.getEasySQL("select t1.EVALUATE,t1.EVALUATE2,t1.EVALUATE3,t1.EVALUATE_EXPLAIN,t1.INSPECTOR_ACC INSPECTOR,t1.INSPECTOR_NAME,t1.QC_TIME,t1.AGENT_NAME,"
					+ "t1.TASK_NAME,t1.PASS_SCORE,t1.SCORE,t1.PASS_FLAG,t1.BASE_SCORE,t1.RESULT_DESC,t1.LINK_KM_JSON,t2.RG_CLASS_ID AS CLASS_ID,t1.CACH_RESULT,t1.CACH_EXJOSN,t1.CACH_STATE,t1.CACH_BAKUP,t1.CACH_ACC,t1.CACH_TIME  from ");
			sql.append(getTableName("CC_QC_RESULT t1"));
			sql.append("LEFT JOIN " + getTableName("CC_QC_TASK t2") + " on t2.ID=t1.TASK_ID");
			sql.append(" where 1=1  ");
			//2021-02-26 qc-record-checked.jsp页面调用，将 qcResultId 改为 record.QC_RESULT_ID，不知道会不会影响其他页面
			sql.append(qcResultId, " and t1.QC_RESULT_ID = ?", false);
		}else {
			sql = this.getEasySQL("select t1.INSPECTOR_NAME,t1.AGENT_NAME,t2.RG_CLASS_ID AS CLASS_ID from ");
			sql.append(getTableName("CC_QC_TASK_OBJ t1"));
			sql.append("LEFT JOIN " + getTableName("CC_QC_TASK t2") + " on t2.ID=t1.TASK_ID");
			sql.append(" where 1=1  ");
			sql.append(objId, " and t1.ID = ?", false);
		}
		JSONObject result = new JSONObject();
		JSONObject result2 = new JSONObject();
		JSONObject result3 = new JSONObject();
		EasySQL sql2 = new EasySQL("select SR_REF_DESC,SUMMARY_FULL_PATH,EX1,EX2,EX3,EX4,EX5 from  "+this.getTableName("C_CALL_SUMMARY_RECORD")+" WHERE 1=1 ");
		sql2.append(param.getString("serialId"),"and REF_ID=?");
		logger.info("sql2"+sql2.getSQL()+"   参数"+param.getString("serialId"));
		EasySQL sql3 = new EasySQL(" select SOURCE,SOURCE1,SOURCE_EXPLAIN from "+this.getTableName("cc_qc_task_obj")+" where 1=1 ");
		if(CommonUtil.isNotBlank(qcResultId)) {
			sql3.append(qcResultId, " and RG_RESULT_ID = ? ", false);
		}else {
			sql3.append(objId, " and ID = ? ", false);
		}
		
		try {
			result = queryForRecord(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			result2 = queryForRecord(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
			result3 = queryForRecord(sql3.getSQL(),sql3.getParams(),new JSONMapperImpl());
			JSONObject data =  result.getJSONObject("data");
			JSONObject data2 =  result2.getJSONObject("data");
			JSONObject data3 = result3.getJSONObject("data");
			logger.info("查询数据后data2"+data2);
			logger.info("查询结果"+result2);
			data.putAll(data2);
			data.putAll(data3);
			logger.info("合并后data"+data);
			String serialId = param.getString("serialId");
			if(data != null && Constants.showLinkOrder() && CommonUtil.isNotBlank(serialId)) {
				//查找绑定工单服务
				String serviceId = "CF_ORDER_CALL";
				JSONObject serviceParam = new JSONObject();
				serviceParam.put("callId", serialId);
				serviceParam.put("serialId", serialId);
				serviceParam.put("schema", user.getSchemaName());
				serviceParam.put("entId", user.getEpCode());
				serviceParam.put("busiOrderId", user.getBusiOrderId());
				JSONObject invokeResult = ServiceUtil.invoke(serviceId, serviceParam);
				if(invokeResult != null) {
					if(ServerContext.isDebug()) {
						logger.info(CommonUtil.getClassNameAndMethod(this) + "获取工单信息serviceId="+serviceId);
						logger.info(CommonUtil.getClassNameAndMethod(this) + invokeResult.toJSONString());
					}
					List<JSONObject> orderLinks = new ArrayList<JSONObject>();
					String type = invokeResult.getString("type");
					String url = invokeResult.getString("url");
					if(url != null && !url.contains("?")) {
						url = url + "?";
					}
					if(StringUtils.equals(type, "1")) {
						String orderId = invokeResult.getString("orderId");
						if(CommonUtil.isNotBlank(url) && CommonUtil.isNotBlank(orderId) ) {
							String path = url + "&orderId="+orderId;
							data.put("orderType", type);
							JSONObject link = new JSONObject();
							link.put("orderType", type);
							link.put("orderId", orderId);
							link.put("linkTitle", StringUtil.nvl(invokeResult.getString("linkTitle"), orderId));
							link.put("tabId", StringUtil.nvl(invokeResult.getString("tabId")));
							link.put("tabTitle", StringUtil.nvl(invokeResult.getString("tabTitle"), "工单信息"));
							link.put("path", path);
							orderLinks.add(link);
							
						}
					}else if(StringUtils.equals(type, "2")){
						try{
							JSONArray orderIds = invokeResult.getJSONArray("orderIds");
							if(CommonUtil.isNotBlank(url) && CommonUtil.listIsNotNull(orderIds) ) {
								result.put("orderType", type);
								for(int i=0; i<orderIds.size(); i++) {
									String orderId = orderIds.getString(i);
									String path = url + "&orderId="+orderId;
									JSONObject link = new JSONObject();
									link.put("orderType", type);
									link.put("orderId", orderId);
									link.put("linkTitle", StringUtil.nvl(invokeResult.getString("linkTitle"), orderId));
									link.put("tabId", StringUtil.nvl(invokeResult.getString("tabId")));
									link.put("tabTitle", StringUtil.nvl(invokeResult.getString("tabTitle"), "工单信息"));
									link.put("path", path);
									orderLinks.add(link);
								}
							}
						}catch (Exception e) {
							logger.error(CommonUtil.getClassNameAndMethod(this) + "获取工单错误", e);
						}
					}
					data.put("orderLinks", orderLinks);
				}else {
					logger.error(CommonUtil.getClassNameAndMethod(this)
							+ "接口响应为空serviceId="+serviceId+"serialId"+serialId);
				}
			}
			if(result != null) {
				result.put("data", data);
			}
		}catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)
					+ "获取信息错误！", e);
		}
		return result;
	}
	
	/**
	 * 任务对象
	 * @return
	 */
	@WebControl(name="AdminHandleInfo", type=Types.RECORD)
	public JSONObject groupRecord(){
		String id = param.getString("ID");
		try {
			EasySQL sql = this.getEasySQL("select REASON_STATUS, REASON_CONTENT from ");
			sql.append(getTableName("CC_QC_ADMIN_SELECT"));
			sql.append("where 1=1 ");
			sql.append(id, "and ID = ?", false);
			logger.info(sql.getSQL()+","+JSON.toJSONString(sql.getParams()));
			return queryForRecord(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("REASON_STATUS", "");
			jsonObject.put("REASON_CONTENT", "");
			return jsonObject;
		}
	}

	//质检结果项明细
	@WebControl(name="itemTemplate", type=Types.TEMPLATE)
	public JSONObject itemTemplate(){
		EasySQL sql = this.getEasySQL("select t1.ITEM_NAME,t1.SCORE,t1.ITEM_DESC,t2.STD_SCORE,t1.RESULT_ITEM_ID from ");
		sql.append(getTableName("CC_QC_RESULT_ITEM t1,"));
		sql.append(getTableName("CC_QC_ITEM t2"));
		sql.append(" where 1=1 and t1.ITEM_ID = t2.ITEM_ID");
		sql.append(param.getString("qcResultId")," and t1.QC_RESULT_ID = ?", false);
		return queryForList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 质检结果 质检项命中明细
	 * @return
	 */
	@WebControl(name="resultItemList",type=Types.TEMPLATE)
	public JSONObject resultItemList(){
		String qcResultId = param.getString("qcResultId");
		JSONArray array=new JSONArray();
		try {
			String sql = "select RESULT_ITEM_ID as SUBJECT_ID,ITEM_NAME as SUBJECT_NAME,IS_ONE_VOTE,ITEM_DESC as SUBJECT_DESC,P_ITEM_ID,VALID_STATE,ITEM_ID as QC_ITEM_ID,IS_RADIO from "+getTableName("CC_QC_RESULT_ITEM")+" where QC_RESULT_ID = ? and P_ITEM_ID = '2000' ORDER BY INDEX_NUM ASC";
			List<JSONObject> list = this.getQuery().queryForList(sql, new Object[]{qcResultId}, new JSONMapperImpl());
			sql = "select t1.*,t2.ITEM_TYPE,T2.SCORE_TYPE from "+getTableName("CC_QC_RESULT_ITEM t1")+" left join "+getTableName("CC_QC_ITEM t2")+" on t1.ITEM_ID = t2.ITEM_ID where t1.QC_RESULT_ID = ? and t1.P_ITEM_ID = ? ORDER BY t1.INDEX_NUM ASC";
			for(JSONObject json:list){
				String subjectId = json.getString("SUBJECT_ID");
				List<Map<String, String>> childlist=this.getQuery().queryForList(sql, new Object[]{qcResultId,subjectId},new MapRowMapperImpl());
				json.put("list", childlist);
				array.add(json);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return getTemplate(array);
	}
	
	@WebControl(name="getResultInfo", type=Types.RECORD)
	public JSONObject getResultInfo(){
		EasySQL sql = this.getEasySQL("select QC_RESULT_ID,SERIAL_ID from");
		sql.append(getTableName("CC_QC_RESULT"));
		sql.append("where 1=1");
		sql.append(param.getString("score"), "and SCORE = ?");
		return this.queryForRecord(sql.getSQL(), sql.getParams(), null);
	}
	
	// 智能质检结果
	@InfAuthCheck(resId = {"cc-qc-znzjjg"},msg = "您无权访问")
	@WebControl(name = "znResultList", type = Types.LIST)
	public JSONObject znResultList() {
		try {
			UserModel user = UserUtil.getUser(request);
			EasyQuery query =  getQuery();
			EasySQL sql = QcResultSql.getZnResultListSql(user, param, query);
			return queryForPageList(sql.getSQL(), sql.getParams());
		}catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)
					+ "查找智能质检结果错误！" + e.getMessage(), e);
		}
		return null;
	}
	
	/**
	 * 智能质检明细列表
	 * @return
	 */
	@WebControl(name = "znResultInfoList", type = Types.LIST)
	public JSONObject znResultInfoList() {
		try {
			String taskId = param.getString("task");
			if(CommonUtil.isNotBlank(taskId)) {
				String[] c_t = taskId.split(",");
				if(c_t != null && c_t.length > 1) {
					taskId = c_t[1];
				}
			}
			if(StringUtils.isBlank(taskId)) {
				return EasyResult.fail("请先选择质检任务！");
			}
			UserModel user = UserUtil.getUser(request);
			EasyQuery query = getQuery();
			EasySQL sql = QcResultSql.getZnResultInfoListSql(user, param, query);
			JSONObject result = queryForPageList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			
			JSONArray datas = result.getJSONArray("data");
			if(CommonUtil.listIsNotNull(datas)) {
				StringJoiner joiner = new StringJoiner("','");
				for(int i=0;i<datas.size();i++) {
					JSONObject json = datas.getJSONObject(i);
					String id = json.getString("ID");
					joiner.add(id);
				};
				EasySQL sql1 = new EasySQL("select * from " + getTableName("cc_qc_details"));
				sql1.append(" WHERE CAPACITY_ID in ('" + joiner.toString() + "')");
				sql1.append(" ORDER BY ITEMNAME_ID");
				List<JSONObject> details = query.queryForList(sql1.getSQL(), sql1.getParams(), new JSONMapperImpl());
				Map<String, List<JSONObject>> detailsMap = new HashMap<String, List<JSONObject>>();
				for (JSONObject json : details) {
					String capacityId = json.getString("CAPACITY_ID");
					List<JSONObject> cols = detailsMap.get(capacityId);
					if(CommonUtil.listIsNull(cols)) {
						cols = new ArrayList<JSONObject>();
					}
					cols.add(json);
					detailsMap.put(capacityId, cols);
				}
				JSONArray data = new JSONArray();
				for (Object obj : datas) {
					JSONObject json = (JSONObject) obj;
					String capacityId = json.getString("ID");
					List<JSONObject> cols = detailsMap.get(capacityId);
					if(CommonUtil.listIsNotNull(cols)) {
						for (JSONObject detail : cols) {
							json.put("ITEM_NAME_" + detail.getString("ITEMNAME_ID"), detail.getString("SCORE"));
						}
					}
					data.add(json);
				}
				result.put("data", data);
			}
			//查找任务的智能质检明细项
			return result;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "查找智能质检结果错误！" + e.getMessage(), e);
		}
		return null;
	}
	
	/**
	 * 智能质检明细项列表表头
	 * @return
	 */
	@WebControl(name = "znResultInfoTableCols", type = Types.LIST)
	public JSONObject znResultInfoTableCols() {
		JSONObject result = new JSONObject();
		try {
			String taskId = param.getString("task");
			if(CommonUtil.isNotBlank(taskId)) {
				String[] c_t = taskId.split(",");
				if(c_t != null && c_t.length > 1) {
					taskId = c_t[1];
				}
			}
			if(StringUtils.isBlank(taskId)) {
				return EasyResult.fail("请先选择质检任务！");
			}
			UserModel user = UserUtil.getUser(request);
			EasyQuery query = getQuery();
			EasySQL sql = QcResultSql.getZnResultInfoListSql(user, param, query);
			List<EasyRow> resultList = query.queryForList(sql.getSQL(), sql.getParams(), 1, 1);;
			if (CommonUtil.listIsNotNull(resultList)) {
				String id = resultList.get(0).getColumnValue("ID");
				EasySQL sql1 = new EasySQL("select * from " + getTableName("cc_qc_details"));
				sql1.append(" WHERE CAPACITY_ID= '" + id + "'");
				sql1.append(" ORDER BY ITEMNAME_ID ");
				List<JSONObject> details = query.queryForList(sql1.getSQL(), sql1.getParams(), new JSONMapperImpl());
				result.put("cols", details);
			}
		}catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "智能质检明细项列表表头错误！" + e.getMessage(), e);
		}
		return result;
	}




	/**
	 * 评分项统计报表
	 * @return
	 */
	@InfAuthCheck(resId = {"cc-qc-zx-pfxtj"},msg = "您无权访问")
	@WebControl(name = "qcResultStat", type = Types.PAGE)
	public JSONObject qcResultStat(){
		try {
			String taskId = param.getString("taskId");
			if(StringUtil.isBlank(taskId)) {
				return EasyResult.fail(getI18nValue("请先选择质检任务！"));
			}
			String startCreateTime = param.getString("beginStartDate") + " 00:00:00";
			String endCreateTime = param.getString("endStartDate") + " 23:59:59";
			if(StringUtil.isBlank(startCreateTime) || StringUtil.isBlank(endCreateTime)) {
				return EasyResult.fail(getI18nValue("时间查询范围不能为空"));
			}
			int days = DateUtil.bwDays(startCreateTime, endCreateTime, DateUtil.TIME_FORMAT);
			if(days < 0 || days >31) {
				return EasyResult.fail(getI18nValue("时间查询范围不能大于31天"));
			}
			String callI18n = getI18nValue("话务");
            String mediaI18n = getI18nValue("全媒体");
			//获取基本信息统计
			EasySQL sql = QcStatSql.getTaskObjStatByTaskAndUser(getUser(),param, this.getQuery().getTypes(),callI18n,mediaI18n);
			JSONObject taskObjStatPage = queryForPageList(sql.getSQL(),sql.getParams());
			JSONArray taskObjStatArray = taskObjStatPage.getJSONArray("data");

			//获取评分项统计信息
			StatQuery.getItemInfoStat(getQuery(),getUser(),param,ItemEnum.ItemTypeEnum.SCORING_ITEM,taskObjStatPage, taskObjStatArray);
			return taskObjStatPage;
		}catch (Exception e){
			logger.error(CommonUtil.getClassNameAndMethod(this) + "查询评分项统计报表失败！" + e.getMessage(), e);
			return EasyResult.fail(getI18nValue("查询评分项统计报表失败"));
		}
	}
	/**
	 * 评分细项统计报表
	 * @return
	 */
	@InfAuthCheck(resId = {"cc-qc-zx-pfxxtj"},msg = "您无权访问")
	@WebControl(name = "qcResultItemStat", type = Types.PAGE)
	public JSONObject qcResultItemStat(){
		try {
			String taskId = param.getString("taskId");
			if(StringUtil.isBlank(taskId)) {
				return EasyResult.fail(getI18nValue("请先选择质检任务！"));
			}
			String startCreateTime = param.getString("beginStartDate") + " 00:00:00";
			String endCreateTime = param.getString("endStartDate") + " 23:59:59";
			if(StringUtil.isBlank(startCreateTime) || StringUtil.isBlank(endCreateTime)) {
				return EasyResult.fail(getI18nValue("时间查询范围不能为空"));
			}
			int days = DateUtil.bwDays(startCreateTime, endCreateTime, DateUtil.TIME_FORMAT);
			if(days < 0 || days >31) {
				return EasyResult.fail(getI18nValue("时间查询范围不能大于31天"));
			}
			String callI18n = getI18nValue("话务");
            String mediaI18n = getI18nValue("全媒体");
			//获取基本信息统计
			EasySQL sql = QcStatSql.getTaskObjStatByTaskAndUser(getUser(),param, this.getQuery().getTypes(),callI18n,mediaI18n);
			JSONObject taskObjStatPage = queryForPageList(sql.getSQL(),sql.getParams());
			JSONArray taskObjStatArray = taskObjStatPage.getJSONArray("data");

			//获取评分项统计信息
			StatQuery.getItemInfoStat(getQuery(),getUser(),param,ItemEnum.ItemTypeEnum.SCORING_ITEM_DETAILS,taskObjStatPage, taskObjStatArray);
			return taskObjStatPage;
		}catch (Exception e){
			logger.error(CommonUtil.getClassNameAndMethod(this) + "查询评分项统计报表失败！" + e.getMessage(), e);
			return EasyResult.fail(getI18nValue("查询评分细项统计报表失败"));
		}
	}

	/**
	 * 查询质检评分表头
	 * @return
	 */
	@WebControl(name="getItemInfoCols",type= Types.LIST)
	public JSONObject getItemInfoCols() {
		return StatQuery.getItemInfoColsJsonObject(param,getUser(),getQuery());
	}
	/**
	 * 查询质检评分项明细表头
	 * @return
	 */
	@WebControl(name="getItemDetailsInfoCols",type= Types.LIST)
	public JSONObject getItemDetailsInfoCols() {
		return StatQuery.getItemDetailsInfoColsJsonObject(param,getUser(),getQuery());
	}
	
	
	/**
	 * 获取工单质检基本信息
	 * @return
	 */
	@WebControl(name="getQcOrderInfo",type= Types.LIST)
	public JSONObject getQcOrderInfo() {
		JSONObject resultJson = new JSONObject();
		String objId = param.getString("objId");
		String taskId = param.getString("taskId");
		String classId = param.getString("classId");
		String orderId = param.getString("orderId");
		//是否主流程
		String isMain = param.getString("isMain");
		JSONObject qcResult = null;
		try {
			//判断是否有质检结果
			String qcResultId = "";
			JSONObject objJson = null;
			
			EasySQL easySQL = new EasySQL();
			
			easySQL.append("SELECT * FROM "+getTableName("C_BO_BASE_ORDER"));
			easySQL.append(orderId,"WHERE ID =?",false);
			
			easySQL.append("UNION ALL");
			
			easySQL.append("SELECT * FROM "+getTableName("C_BO_BASE_ORDER_HIS"));
			easySQL.append(orderId,"WHERE ID =?",false);
			
			JSONObject orderJson = this.getQuery().queryForRow(easySQL.getSQL(), easySQL.getParams(), new JSONMapperImpl());
			
			
			easySQL = new EasySQL();
			//获取主表数据
			easySQL.append("SELECT * FROM "+getTableName("CC_QC_TASK_OBJ"));
			easySQL.append(objId,"WHERE ID =?",false);
			objJson = this.getQuery().queryForRow(easySQL.getSQL(), easySQL.getParams(), new JSONMapperImpl());
			
			if (!StringUtils.equals("01", isMain)) {
				easySQL = new EasySQL();
				//子流程
				easySQL.append("SELECT * FROM "+getTableName("CC_QC_TASK_ORDER_OBJ"));
				easySQL.append(objId,"WHERE PARENT_OBJ_ID =?",false);
				easySQL.append(orderId,"AND SERIAL_ID =?",false);
				JSONObject subObjJson = this.getQuery().queryForRow(easySQL.getSQL(), easySQL.getParams(), new JSONMapperImpl());
				logger.info(" subObjJson : "+subObjJson);
				if (subObjJson == null) {
					subObjJson = new JSONObject();
					//工单负责人账号
					String orderCharge = orderJson.getString("ORDER_CHARGE");
					//获取工单负责人姓名
					easySQL = new EasySQL();
					easySQL.append(orderCharge,"select t2.USER_ID AGENT_ID,t2.USERNAME AGENT_PHONE,t2.USER_ACCT AGENT_NAME,t2.USER_ACCT AGENT_ACC from CC_USER t2 where  t2.USER_ACCT =?");
					JSONObject userJson = this.getQuery().queryForRow(easySQL.getSQL(), easySQL.getParams(), new JSONMapperImpl());
					logger.info(" userJson : "+userJson);
					if (userJson!=null) {
						subObjJson.put("AGENT_NAME", userJson.getString("AGENT_PHONE"));
					}
					subObjJson.put("INSPECTOR_NAME", objJson.getString("INSPECTOR_NAME")); 
					subObjJson.put("SOURCE", objJson.getString("SOURCE")); 
				}
				objJson = subObjJson;
			}
			
			
			easySQL = new EasySQL();
			easySQL.append("SELECT * FROM "+getTableName("CC_QC_CLASS"));
			easySQL.append(classId,"WHERE CLASS_ID =?",false);
			JSONObject classJson = this.getQuery().queryForRow(easySQL.getSQL(), easySQL.getParams(), new JSONMapperImpl());
			
			JSONObject baseInfo = new JSONObject();
			if (objJson!=null) {
				baseInfo.put("AGENT_NAME", objJson.getString("AGENT_NAME"));
				baseInfo.put("INSPECTOR_NAME", objJson.getString("INSPECTOR_NAME"));
				baseInfo.put("RG_QC_TIME", objJson.getString("RG_QC_TIME"));
				baseInfo.put("SOURCE", objJson.getString("SOURCE"));
				
				qcResultId = objJson.getString("RG_RESULT_ID");
				
				easySQL = new EasySQL();
				easySQL.append("SELECT * FROM "+getTableName("CC_QC_RESULT"));
				easySQL.append(objJson.getString("RG_RESULT_ID"),"WHERE QC_RESULT_ID =?",false);
				qcResult = this.getQuery().queryForRow(easySQL.getSQL(), easySQL.getParams(), new JSONMapperImpl());
				logger.info("  CC_QC_RESULT :"+easySQL.getSQL()+", param:"+JSON.toJSONString(easySQL.getParams())+",qcResult:"+qcResult);
				if (qcResult!=null) {
					baseInfo.put("PASS_FLAG", qcResult.getString("PASS_FLAG"));
				}
				
			}
			if (orderJson!=null){
				baseInfo.put("END_TIME", orderJson.getString("END_TIME"));
			}
			
			if (classJson!=null){
				baseInfo.put("PASS_SCORE", classJson.getString("PASS_SCORE"));
				baseInfo.put("TOTAL_SCORE", classJson.getString("TOTAL_SCORE"));
			}
			resultJson.put("baseInfo", baseInfo);
			
			
			
			JSONArray array=new JSONArray();
			try {
				if(StringUtils.notBlank(qcResultId)) {
					String sql = "select RESULT_ITEM_ID as SUBJECT_ID,ITEM_NAME as SUBJECT_NAME,IS_ONE_VOTE,ITEM_DESC as SUBJECT_DESC,P_ITEM_ID,VALID_STATE,'1' as RESULT_ID,ITEM_ID as QC_ITEM_ID,IS_RADIO  from "+getTableName("CC_QC_RESULT_ITEM")+" where QC_RESULT_ID = ? and P_ITEM_ID = '2000' ORDER BY INDEX_NUM ASC";
					List<JSONObject> list = this.getQuery().queryForList(sql, new Object[]{qcResultId}, new JSONMapperImpl());
					sql = "select T1.*,T1.SCORE AS STD_SCORE,'1' as RESULT_ID,T2.ITEM_TYPE,T2.SCORE_TYPE from "+getTableName("CC_QC_RESULT_ITEM T1")+" left join "+getTableName("CC_QC_ITEM T2")+" on T1.ITEM_ID = T2.ITEM_ID where T1.QC_RESULT_ID = ? and T1.P_ITEM_ID = ? ORDER BY T1.INDEX_NUM ASC";
					for(JSONObject json:list){
						String subjectId = json.getString("SUBJECT_ID");
						List<Map<String, String>> childlist=this.getQuery().queryForList(sql, new Object[]{qcResultId,subjectId},new MapRowMapperImpl());
						json.put("list", childlist);
						array.add(json);
					}
				}else {
	
					//肯定是一级
					EasySQL sql = new EasySQL("select DISTINCT t1.CREATE_TIME,t1.INDEX_NUM,t2.F_LEVEL,t1.ITEM_ID as SUBJECT_ID,t1.ITEM_NAME as SUBJECT_NAME,t1.ITEM_DESC as SUBJECT_DESC,t1.P_ITEM_ID,t1.ITEM_ID as QC_ITEM_ID,t1.IS_RADIO from "+getTableName("CC_QC_ITEM t1 "));
					sql.append(" left join "+getTableName("CC_QC_CLASS_ADVICE t2 on t1.ITEM_ID = t2.ITEM_ID1 and t2.F_LEVEL = '1' "));
					sql.append(classId," where t1.CLASS_ID = ? and t1.P_ITEM_ID = '2000' ");
					sql.append("order by t1.INDEX_NUM ASC, t1.CREATE_TIME ASC ");
	
					List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
					for(JSONObject json:list){
						String subjectId = json.getString("SUBJECT_ID");
			
						EasySQL sql2 = new EasySQL("select t2.F_LEVEL,t1.*  from "+getTableName("CC_QC_ITEM t1 "));
						sql2.append(" left join "+getTableName("CC_QC_CLASS_ADVICE t2 on t1.ITEM_ID = t2.ITEM_ID2 and t2.F_LEVEL = '2' "));
						sql2.append(classId," where t1.CLASS_ID = ? ");
						sql2.append(subjectId," and t1.P_ITEM_ID = ? ");
						sql2.append(" order by t1.INDEX_NUM ASC, t1.CREATE_TIME ASC");
						
						
						List<Map<String, String>> childlist=this.getQuery().queryForList(sql2.getSQL(), sql2.getParams(),new MapRowMapperImpl());
						json.put("list", childlist);
						array.add(json);
					}
				}
			} catch (SQLException e) {
				LogUtil.addSystemErrorLog(getUser(), Constants.APP_NAME, "查询评分结果异常:"+e.getMessage(), e,logger);
				this.error(e.getMessage(), e);
			}
			JSONObject template = getTemplate(array);
			resultJson.put("checkList", template);
			resultJson.put("qcResult", qcResult);
			resultJson.put("objJson", objJson);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " " + e.getMessage(), e);
		}
		
		return resultJson;
	}

	@InfAuthCheck(resId ={"cc-qc-zxfdgl"}, msg = "您无权访问!")
	@WebControl(name="cachList", type= Types.LIST)
	public JSONObject cachList(){
		UserModel user = UserUtil.getUser(request);
		logger.info("[QcResultDao.cachList].param:" + param);
		EasySQL sql = QcResultSql.getCachListSql(user, param, request,getQuery());
		logger.info(CommonUtil.getClassNameAndMethod(this) + "获取所有需要坐席辅导数据sql:" +sql.getSQL()+":"+ JSON.toJSONString(sql.getParams()));
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 质检全量统计-质检数量统计报表
	 * 包含人工/智能/人工+智能数量统计报表
	 */
	@InfAuthCheck(resId ={"cc-qc-statistics"}, msg = "您无权访问!")
	@WebControl(name="qcCountStat", type= Types.LIST)
	public JSONObject qcCountStat(){
		try {
			String startTime = param.getString("beginStartDate");
			String endTime = param.getString("endStartDate");
			
			// 参数校验
			if(StringUtil.isBlank(startTime) || StringUtil.isBlank(endTime)) {
				return EasyResult.fail(getI18nValue("时间查询范围不能为空"));
			}

			JSONObject data = new JSONObject();
			// 查询人工质检统计数据
			EasySQL manualSql = new EasySQL();
			manualSql.append("SELECT");
			manualSql.append("    v.call_type_name,");
			manualSql.append("    IFNULL( count( t1.QC_RESULT_ID ), 0 ) AS qc_count,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t1.SCORE > 95 THEN 1 ELSE 0 END ), 0 ) AS pass_count,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t1.SCORE > 80 AND t1.SCORE <= 95 THEN 1 ELSE 0 END ), 0 ) AS nofatal_count,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t1.SCORE <= 80 THEN 1 ELSE 0 END ), 0 ) AS fatal_count,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t1.SCORE <= 80 THEN 1 ELSE 0 END ) / NULLIF( count( t1.QC_RESULT_ID ), 0 ), 0 ) AS fatal_rate,");
			manualSql.append("    IFNULL(SUM( CASE WHEN t1.SCORE > 80 AND t1.SCORE <= 95 THEN 1 ELSE 0 END ) / NULLIF( count( t1.QC_RESULT_ID ), 0 ),0) AS nofatal_rate,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t1.SCORE <= 95 THEN 1 ELSE 0 END ) / NULLIF( count( t1.QC_RESULT_ID ), 0 ), 0 ) AS all_rate,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t1.CACH_STATE = 2 THEN 1 ELSE 0 END ), 0 ) AS cach_count,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t2.RECONSIDER_FLAG > 0 THEN 1 ELSE 0 END ), 0 ) AS approval_count,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t2.RECONSIDER_FLAG = 3 THEN 1 ELSE 0 END ), 0 ) AS approval_consistency_count,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t2.RECONSIDER_FLAG IN ( 2, 4 ) THEN 1 ELSE 0 END ), 0 ) AS approval_inconsistency_count,");
			manualSql.append("    IFNULL(SUM( CASE WHEN t2.RECONSIDER_FLAG IN ( 2, 4 ) THEN 1 ELSE 0 END ) / NULLIF( SUM( CASE WHEN t2.RECONSIDER_FLAG > 0 THEN 1 ELSE 0 END ), 0 ),0) AS approval_finish_rate");
			manualSql.append(" FROM ( SELECT 1 AS CALL_TYPE, '话务' AS call_type_name UNION ALL SELECT 2, '新媒体' UNION ALL SELECT 4, '工单' ) v");
			manualSql.append("     LEFT JOIN " + getTableName("cc_qc_result") + " t1 ON t1.CALL_TYPE = v.CALL_TYPE");
			manualSql.append("     AND t1.QC_TIME >= ? AND t1.QC_TIME <= ?");
			manualSql.append("     LEFT JOIN " + getTableName("cc_qc_task_obj") + " t2 ON t1.QC_RESULT_ID = t2.RG_RESULT_ID");
			manualSql.append(" GROUP BY v.CALL_TYPE, v.call_type_name");
			manualSql.append(" UNION ALL");
			manualSql.append(" SELECT '总计' AS call_type_name,");
			manualSql.append("    IFNULL( count( 0 ), 0 ) AS qc_count,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t1.SCORE > 95 THEN 1 ELSE 0 END ), 0 ) AS pass_count,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t1.SCORE > 80 AND t1.SCORE <= 95 THEN 1 ELSE 0 END ), 0 ) AS nofatal_count,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t1.SCORE <= 80 THEN 1 ELSE 0 END ), 0 ) AS fatal_count,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t1.SCORE <= 80 THEN 1 ELSE 0 END ) / NULLIF( count( 0 ), 0 ), 0 ) AS fatal_rate,");
			manualSql.append("    IFNULL(SUM( CASE WHEN t1.SCORE > 80 AND t1.SCORE <= 95 THEN 1 ELSE 0 END ) / NULLIF( count( 0 ), 0 ),0) AS nofatal_rate,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t1.SCORE <= 95 THEN 1 ELSE 0 END ) / NULLIF( count( 0 ), 0 ), 0 ) AS all_rate,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t1.CACH_STATE = 2 THEN 1 ELSE 0 END ), 0 ) AS cach_count,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t2.RECONSIDER_FLAG > 0 THEN 1 ELSE 0 END ), 0 ) AS approval_count,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t2.RECONSIDER_FLAG = 3 THEN 1 ELSE 0 END ), 0 ) AS approval_consistency_count,");
			manualSql.append("    IFNULL( SUM( CASE WHEN t2.RECONSIDER_FLAG IN ( 2, 4 ) THEN 1 ELSE 0 END ), 0 ) AS approval_inconsistency_count,");
			manualSql.append("    IFNULL(SUM( CASE WHEN t2.RECONSIDER_FLAG IN ( 2, 4 ) THEN 1 ELSE 0 END ) / NULLIF( SUM( CASE WHEN t2.RECONSIDER_FLAG > 0 THEN 1 ELSE 0 END ), 0 ),0) AS approval_finish_rate");
			manualSql.append(" FROM " + getTableName("cc_qc_result") + " t1");
			manualSql.append("     LEFT JOIN " + getTableName("cc_qc_task_obj") + " t2 ON t1.QC_RESULT_ID = t2.RG_RESULT_ID");
			manualSql.append(" WHERE t1.QC_TIME >= ? AND t1.QC_TIME <= ?");
			
			// 查询智能质检统计数据
			EasySQL aiSql = new EasySQL();
			aiSql.append("SELECT");
			aiSql.append("    v.call_type_name,");
			aiSql.append("    IFNULL( count( t1.ID ), 0 ) AS qc_count,");
			aiSql.append("    IFNULL( SUM( CASE WHEN t1.SCORE > 95 THEN 1 ELSE 0 END ), 0 ) AS pass_count,");
			aiSql.append("    IFNULL( SUM( CASE WHEN t1.SCORE > 80 AND t1.SCORE <= 95 THEN 1 ELSE 0 END ), 0 ) AS nofatal_count,");
			aiSql.append("    IFNULL( SUM( CASE WHEN t1.SCORE <= 80 THEN 1 ELSE 0 END ), 0 ) AS fatal_count,");
			aiSql.append("    IFNULL( SUM( CASE WHEN t1.SCORE <= 80 THEN 1 ELSE 0 END ) / NULLIF( count( t1.ID ), 0 ), 0 ) AS fatal_rate,");
			aiSql.append("    IFNULL(SUM( CASE WHEN t1.SCORE > 80 AND t1.SCORE <= 95 THEN 1 ELSE 0 END ) / NULLIF( count( t1.ID ), 0 ),0) AS nofatal_rate,");
			aiSql.append("    IFNULL( SUM( CASE WHEN t1.SCORE <= 95 THEN 1 ELSE 0 END ) / NULLIF( count( t1.ID ), 0 ), 0 ) AS all_rate,");
			aiSql.append("    0 AS cach_count,");
			aiSql.append("    0 AS approval_count,");
			aiSql.append("    0 AS approval_consistency_count,");
			aiSql.append("    0 AS approval_inconsistency_count,");
			aiSql.append("    0 AS approval_finish_rate");
			aiSql.append(" FROM ( SELECT 1 AS CALL_TYPE, '话务' AS call_type_name UNION ALL SELECT 2, '新媒体' UNION ALL SELECT 4, '工单' ) v");
			aiSql.append("     LEFT JOIN " + getTableName("cc_qc_capacity") + " t1 ON t1.CALL_TYPE = v.CALL_TYPE");
			aiSql.append("     AND t1.QC_TIME >= ? AND t1.QC_TIME <= ?");
			aiSql.append(" GROUP BY v.CALL_TYPE, v.call_type_name");
			aiSql.append(" UNION ALL");
			aiSql.append(" SELECT '总计' AS call_type_name,");
			aiSql.append("    IFNULL( count( 0 ), 0 ) AS qc_count,");
			aiSql.append("    IFNULL( SUM( CASE WHEN SCORE > 95 THEN 1 ELSE 0 END ), 0 ) AS pass_count,");
			aiSql.append("    IFNULL( SUM( CASE WHEN SCORE > 80 AND SCORE <= 95 THEN 1 ELSE 0 END ), 0 ) AS nofatal_count,");
			aiSql.append("    IFNULL( SUM( CASE WHEN SCORE <= 80 THEN 1 ELSE 0 END ), 0 ) AS fatal_count,");
			aiSql.append("    IFNULL( SUM( CASE WHEN SCORE <= 80 THEN 1 ELSE 0 END ) / NULLIF( count( 0 ), 0 ), 0 ) AS fatal_rate,");
			aiSql.append("    IFNULL( SUM( CASE WHEN SCORE > 80 AND SCORE <= 95 THEN 1 ELSE 0 END ) / NULLIF( count( 0 ), 0 ), 0 ) AS nofatal_rate,");
			aiSql.append("    IFNULL( SUM( CASE WHEN SCORE <= 95 THEN 1 ELSE 0 END ) / NULLIF( count( 0 ), 0 ), 0 ) AS all_rate,");
			aiSql.append("    0 AS cach_count,");
			aiSql.append("    0 AS approval_count,");
			aiSql.append("    0 AS approval_consistency_count,");
			aiSql.append("    0 AS approval_inconsistency_count,");
			aiSql.append("    0 AS approval_finish_rate");
			aiSql.append(" FROM " + getTableName("cc_qc_capacity"));
			aiSql.append(" WHERE QC_TIME >= ? AND QC_TIME <= ?");
			aiSql.append(" AND CALL_TYPE is not NULL");

			logger.info(CommonUtil.getClassNameAndMethod(this) + "查询人工质检统计数据sql:" + manualSql.getSQL() + ",参数为:" + JSONObject.toJSONString(new Object[]{startTime, endTime, startTime, endTime}));
			logger.info(CommonUtil.getClassNameAndMethod(this) + "查询智能质检统计数据sql:" + aiSql.getSQL() + ",参数为:" + JSONObject.toJSONString(new Object[]{startTime, endTime, startTime, endTime}));
			
			// 执行查询
			JSONObject manualResult = queryForList(manualSql.getSQL(), new Object[]{startTime, endTime, startTime, endTime});
			JSONObject aiResult = queryForList(aiSql.getSQL(), new Object[]{startTime, endTime, startTime, endTime});
			
			// 获取数据数组
			JSONArray manualData = manualResult.getJSONArray("data");
			JSONArray aiData = aiResult.getJSONArray("data");
			
			// 计算人工+智能总计数据
			JSONArray totalData = new JSONArray();

			// 处理各个渠道数据
			for(int i = 0; i < manualData.size(); i++) {
				JSONObject manual = manualData.getJSONObject(i);
				JSONObject ai = aiData.getJSONObject(i);
				JSONObject total = new JSONObject();
				
				// 复制渠道名称
				total.put("CALL_TYPE_NAME", manual.getString("CALL_TYPE_NAME"));
				
				// 计算各项指标
				int manualQcCount = manual.getIntValue("QC_COUNT");
				int aiQcCount = ai.getIntValue("QC_COUNT");
				int totalQcCount = manualQcCount + aiQcCount;
				
				total.put("QC_COUNT", totalQcCount);
				total.put("PASS_COUNT", manual.getIntValue("PASS_COUNT") + ai.getIntValue("PASS_COUNT"));
				total.put("NOFATAL_COUNT", manual.getIntValue("NOFATAL_COUNT") + ai.getIntValue("NOFATAL_COUNT"));
				total.put("FATAL_COUNT", manual.getIntValue("FATAL_COUNT") + ai.getIntValue("FATAL_COUNT"));
				
				// 重新计算比率并格式化为两位小数
				if(totalQcCount > 0) {
					BigDecimal fatalRate = new BigDecimal((double)(manual.getIntValue("FATAL_COUNT") + ai.getIntValue("FATAL_COUNT")) / totalQcCount)
							.setScale(2, BigDecimal.ROUND_HALF_UP);
					BigDecimal nofatalRate = new BigDecimal((double)(manual.getIntValue("NOFATAL_COUNT") + ai.getIntValue("NOFATAL_COUNT")) / totalQcCount)
							.setScale(2, BigDecimal.ROUND_HALF_UP);
					BigDecimal allRate = new BigDecimal((double)(manual.getIntValue("FATAL_COUNT") + ai.getIntValue("FATAL_COUNT") +
							manual.getIntValue("NOFATAL_COUNT") + ai.getIntValue("NOFATAL_COUNT")) / totalQcCount)
							.setScale(2, BigDecimal.ROUND_HALF_UP);
					
					total.put("FATAL_RATE", fatalRate.doubleValue());
					total.put("NOFATAL_RATE", nofatalRate.doubleValue());
					total.put("ALL_RATE", allRate.doubleValue());
				} else {
					total.put("FATAL_RATE", 0.00);
					total.put("NOFATAL_RATE", 0.00);
					total.put("ALL_RATE", 0.00);
				}
				
				// 复议和缓存相关指标保持人工质检的数据，并格式化复议完成率
				total.put("CACH_COUNT", manual.getIntValue("CACH_COUNT"));
				total.put("APPROVAL_COUNT", manual.getIntValue("APPROVAL_COUNT"));
				total.put("APPROVAL_CONSISTENCY_COUNT", manual.getIntValue("APPROVAL_CONSISTENCY_COUNT"));
				total.put("APPROVAL_INCONSISTENCY_COUNT", manual.getIntValue("APPROVAL_INCONSISTENCY_COUNT"));
				total.put("APPROVAL_FINISH_RATE", new BigDecimal(manual.getDoubleValue("APPROVAL_FINISH_RATE"))
						.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
				
				totalData.add(total);
			}
			
			// 格式化人工质检数据中的小数
			for(int i = 0; i < manualData.size(); i++) {
				JSONObject manual = manualData.getJSONObject(i);
				manual.put("FATAL_RATE", new BigDecimal(manual.getDoubleValue("FATAL_RATE"))
						.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
				manual.put("NOFATAL_RATE", new BigDecimal(manual.getDoubleValue("NOFATAL_RATE"))
						.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
				manual.put("ALL_RATE", new BigDecimal(manual.getDoubleValue("ALL_RATE"))
						.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
				manual.put("APPROVAL_FINISH_RATE", new BigDecimal(manual.getDoubleValue("APPROVAL_FINISH_RATE"))
						.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
			}
			
			// 格式化智能质检数据中的小数
			for(int i = 0; i < aiData.size(); i++) {
				JSONObject ai = aiData.getJSONObject(i);
				ai.put("FATAL_RATE", new BigDecimal(ai.getDoubleValue("FATAL_RATE"))
						.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
				ai.put("NOFATAL_RATE", new BigDecimal(ai.getDoubleValue("NOFATAL_RATE"))
						.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
				ai.put("ALL_RATE", new BigDecimal(ai.getDoubleValue("ALL_RATE"))
						.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
			}
			
			// 组装结果
			data.put("manual", manualData);
			data.put("ai", aiData);
			data.put("total", totalData);
			logger.info(CommonUtil.getClassNameAndMethod(this) + "质检数量统计报表查询成功,data:" + data.toJSONString());
			return EasyResult.ok(data);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "查询质检数量统计报表失败！" + e.getMessage(), e);
			return EasyResult.fail(getI18nValue("接口出现异常"));
		}
	}

	/**
	 * 质检全量统计-顶层指标统计
	 */
	@InfAuthCheck(resId ={"cc-qc-statistics"}, msg = "您无权访问!")
	@WebControl(name="qcCountStatTop", type= Types.RECORD)
	public JSONObject qcCountStatTop(){
		try {
			String startTime = param.getString("beginStartDate");
			String endTime = param.getString("endStartDate");

			// 参数校验
			if(StringUtil.isBlank(startTime) || StringUtil.isBlank(endTime)) {
				return EasyResult.fail(getI18nValue("时间查询范围不能为空"));
			}

			//质检全量统计-顶层指标统计sql
			EasySQL sql = new EasySQL("SELECT ");
			sql.append(" count(DISTINCT  CASE WHEN t1.AGENT_ID != '' THEN t1.AGENT_ID END ) AS handle_count,");
			sql.append(" COUNT(DISTINCT CASE WHEN INSPECTOR != '' THEN t1.INSPECTOR END) AS inspector_count,");
			sql.append(" SUM(CASE WHEN t1.RG_STATE >= 2 THEN 1 ELSE 0 END) AS need_count,");
			sql.append(" SUM(CASE WHEN t1.RG_STATE IN ('3', '4') THEN 1 ELSE 0 END) AS actual_count,");
			sql.append(" concat(ROUND((SUM(CASE WHEN t1.RG_STATE IN ('3', '4') THEN 1 ELSE 0 END) * 100 / NULLIF(SUM(CASE WHEN t1.RG_STATE >= 2 THEN 1 ELSE 0 END), 0)), 2), '%') AS qc_rate");
			sql.append(" FROM " + getTableName("CC_QC_TASK_OBJ") + " t1");
			sql.append(startTime," WHERE t1.RG_QC_TIME >= ? ");
			sql.append(endTime," AND t1.RG_QC_TIME <= ?");
			sql.append(getEntId(), " AND t1.ENT_ID = ?");
			sql.append(getBusiOrderId(), " AND t1.BUSI_ORDER_ID = ?");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "查询全量统计sql:" + sql.getSQL() + ",参数:" + JSON.toJSONString(sql.getParams()));
			JSONObject data = queryForRecord(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			return EasyResult.ok(data);
		}catch(Exception e){
			logger.info(CommonUtil.getClassNameAndMethod(this) + "查询全量统计失败！" + e.getMessage());
			return EasyResult.fail(getI18nValue("接口出现异常"));
		}
	}

	/**
	 * 问题统计报表查询-人工质检
	 */
	@InfAuthCheck(resId = {"cc-qc-rg-statistics"}, msg = "您无权访问!")
	@WebControl(name = "qcProblemStatByManual", type = Types.LIST)
	public JSONObject qcProblemStatByManual() {
		try {
			String startTime = param.getString("beginStartDate");
			String endTime = param.getString("endStartDate");
			String statDateType = param.getString("statDateType");
			String statColumn = "";

			// 参数校验
			if(StringUtil.isBlank(startTime) || StringUtil.isBlank(endTime)) {
				return EasyResult.fail(getI18nValue("时间查询范围不能为空"));
			}

			if (Constants.DATE_TYPE.equals(statDateType)){
				statColumn = "DATE_VALUE";
			} else if (Constants.WEEK_TYPE.equals(statDateType)){
				statColumn = "WEEK_IN_YEAR";
			} else if (Constants.MONTH_TYPE.equals(statDateType)){
				statColumn = "MONTH_IN_YEAR";
			} else {
				return EasyResult.fail(getI18nValue("statDateType参数错误"));
			}

			EasySQL statSql = new EasySQL("SELECT cc_dim_date."+ statColumn +" AS QC_TIME,AGENT_NAME,");
			statSql.append(" IFNULL(count(t1.QC_RESULT_ID), 0) AS qc_count,");
			statSql.append(" IFNULL(SUM(CASE WHEN t1.SCORE <= 95 THEN 1 ELSE 0 END), 0) AS error_count,");
			statSql.append(" IFNULL(SUM(CASE WHEN t1.SCORE <= 95 THEN 1 ELSE 0 END) / NULLIF(count(t1.QC_RESULT_ID), 0), 0) AS error_rate ");
			statSql.append(" FROM " + getTableName("CC_QC_RESULT t1"));
			statSql.append(" LEFT JOIN " + Constants.getStatSchema() + ".CC_DIM_DATE cc_dim_date on substr( t1.QC_TIME, 1, 10 ) = cc_dim_date.DATE_VALUE ");
			statSql.append(" WHERE 1 = 1");
			statSql.append(startTime," AND t1.QC_TIME >= ?");
			statSql.append(endTime," AND t1.QC_TIME <= ?");
			statSql.append(" GROUP BY cc_dim_date."+ statColumn +",AGENT_NAME");
			statSql.append(" ORDER BY cc_dim_date."+ statColumn);
			logger.info(CommonUtil.getClassNameAndMethod(this) + "查询人工质检问题统计sql:" + statSql.getSQL() + ",参数:" + JSON.toJSONString(statSql.getParams()));
			JSONObject manualResult = queryForPageList(statSql.getSQL(), statSql.getParams(), new JSONMapperImpl());
			//获取统计结果 统计致命与非致命质检项目
			JSONArray manualData = manualResult.getJSONArray("data");
			for(int i = 0; i < manualData.size(); i++) {
				JSONObject manual = manualData.getJSONObject(i);
				String qcTime = manual.getString("QC_TIME");
				String agentName = manual.getString("AGENT_NAME");
				//致命差错统计
				EasySQL fataItemSql = getStatItemSqlByManual(agentName, qcTime, statColumn,true);
				JSONObject fataItemResult = queryForList(fataItemSql.getSQL(), fataItemSql.getParams(), new JSONMapperImpl());
				JSONArray fataItem = fataItemResult.getJSONArray("data");
				manual.put("fataItem", fataItem);
				//非致命差错统计
				EasySQL noFataSql =  getStatItemSqlByManual(agentName, qcTime, statColumn,false);
				JSONObject noFataItemResult = queryForList(noFataSql.getSQL(), noFataSql.getParams(), new JSONMapperImpl());
				JSONArray noFateItem = noFataItemResult.getJSONArray("data");
				manual.put("noFataItem", noFateItem);
			}
			return manualResult;
		} catch (Exception e) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + "查询全量统计失败！" + e.getMessage());
			return EasyResult.fail(getI18nValue("接口出现异常"));
		}
	}

	/**
	 * 获取统计结果 统计致命与非致命质检项目
	 * @param agentName
	 * @param qcTime
	 * @param statColumn
	 * @return 统计sql
	 */
	private EasySQL getStatItemSqlByManual(String agentName, String qcTime, String statColumn,boolean isFata) {
		EasySQL itemSql = new EasySQL("SELECT t2.ITEM_NAME, COUNT(1) AS COUNT");
		itemSql.append(" FROM " + getTableName("CC_QC_RESULT t1"));
		itemSql.append(" LEFT JOIN " + getTableName("CC_QC_RESULT_ITEM t2") + " ON t1.QC_RESULT_ID = t2.QC_RESULT_ID");
		itemSql.append(" LEFT JOIN " + Constants.getStatSchema() + ".CC_DIM_DATE cc_dim_date on substr( t1.QC_TIME, 1, 10 ) = cc_dim_date.DATE_VALUE ");
		itemSql.append(" WHERE 1 = 1");
		if (isFata){
			itemSql.append(" AND t1.SCORE <= 80"); //致命差错统计
		} else {
			itemSql.append(" AND t1.SCORE > 80 AND t1.score <= 95 "); //非致命差错统计
		}
		itemSql.append(" AND t2.P_ITEM_ID != '2000'"); //非父级扣分项
		itemSql.append(agentName," AND t1.AGENT_NAME = ?");
		itemSql.append(qcTime," AND cc_dim_date."+ statColumn +" = ?");
		itemSql.append(" GROUP BY t2.ITEM_NAME");
		logger.info(CommonUtil.getClassNameAndMethod(this) + "查询人工质检问题统计sql:" + itemSql.getSQL() + ",参数:" + JSON.toJSONString(itemSql.getParams()));
		return itemSql;
	}


	/**
	 * 问题统计报表查询-智能质检
	 */
	@InfAuthCheck(resId = {"cc-qc-zn-statistics"}, msg = "您无权访问!")
	@WebControl(name = "qcProblemStatByAi", type = Types.LIST)
	public JSONObject qcProblemStatByAi() {
		try {
			String startTime = param.getString("beginStartDate");
			String endTime = param.getString("endStartDate");
			String statDateType = param.getString("statDateType");
			String statColumn = "";

			// 参数校验
			if(StringUtil.isBlank(startTime) || StringUtil.isBlank(endTime)) {
				return EasyResult.fail(getI18nValue("时间查询范围不能为空"));
			}

			if (Constants.DATE_TYPE.equals(statDateType)){
				statColumn = "DATE_VALUE";
			} else if (Constants.WEEK_TYPE.equals(statDateType)){
				statColumn = "WEEK_IN_YEAR";
			} else if (Constants.MONTH_TYPE.equals(statDateType)){
				statColumn = "MONTH_IN_YEAR";
			} else {
				return EasyResult.fail(getI18nValue("statDateType参数错误"));
			}

			// 优化后的SQL：去除substr函数，使用索引友好的查询
			EasySQL statSql = new EasySQL("SELECT cc_dim_date."+ statColumn +" AS QC_TIME,t1.AGENT_NAME,");
			statSql.append(" IFNULL(count(t1.ID), 0) AS qc_count,");
			statSql.append(" IFNULL(SUM(CASE WHEN t1.SCORE <= 95 THEN 1 ELSE 0 END), 0) AS error_count,");
			statSql.append(" IFNULL(SUM(CASE WHEN t1.SCORE <= 95 THEN 1 ELSE 0 END) / NULLIF(count(t1.ID), 0), 0) AS error_rate ");
			statSql.append(" FROM " + getTableName("cc_qc_capacity t1"));
			// 优化JOIN条件：使用DATE函数而不是substr，允许使用索引
			statSql.append(" LEFT JOIN " + Constants.getStatSchema() + ".CC_DIM_DATE cc_dim_date on DATE(t1.QC_TIME) = cc_dim_date.DATE_VALUE ");
			statSql.append(startTime," WHERE t1.QC_TIME >= ? ");
			statSql.append(endTime," AND t1.QC_TIME <= ? ");
			statSql.append(" GROUP BY cc_dim_date."+ statColumn +",t1.AGENT_NAME");
			statSql.append(" ORDER BY cc_dim_date."+ statColumn);
			logger.info(CommonUtil.getClassNameAndMethod(this) + "查询智能质检问题统计sql:" + statSql.getSQL() + ",参数:" + JSON.toJSONString(statSql.getParams()));
			JSONObject aiResult = queryForPageList(statSql.getSQL(), statSql.getParams(), new JSONMapperImpl());
			//获取统计结果 统计致命与非致命质检项目 - 优化为批量查询
			JSONArray aiData = aiResult.getJSONArray("data");
			if(aiData.size() > 0) {
				// 批量获取致命和非致命错误统计，避免N+1查询问题
				Map<String, JSONArray> fatalItemsMap = getBatchStatItemsByAi(aiData, statColumn, true, startTime, endTime);
				Map<String, JSONArray> nonFatalItemsMap = getBatchStatItemsByAi(aiData, statColumn, false, startTime, endTime);

				// 将批量查询结果分配给对应的记录
				for(int i = 0; i < aiData.size(); i++) {
					JSONObject ai = aiData.getJSONObject(i);
					String qcTime = ai.getString("QC_TIME");
					String agentName = ai.getString("AGENT_NAME");
					String key = qcTime + "_" + agentName;

					ai.put("fataItem", fatalItemsMap.getOrDefault(key, new JSONArray()));
					ai.put("noFataItem", nonFatalItemsMap.getOrDefault(key, new JSONArray()));
				}
			}
			return aiResult;
		} catch (Exception e) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + "查询全量统计失败！" + e.getMessage());
			return EasyResult.fail(getI18nValue("接口出现异常"));
		}
	}

	/**
	 * 批量获取统计结果 统计致命与非致命质检项目 - 优化版本
	 * @param aiData 主查询结果数据
	 * @param statColumn 统计列名
	 * @param isFatal 是否致命错误
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 按"时间_坐席名"为key的统计结果Map
	 */
	private Map<String, JSONArray> getBatchStatItemsByAi(JSONArray aiData, String statColumn, boolean isFatal, String startTime, String endTime) {
		Map<String, JSONArray> resultMap = new HashMap<>();

		if(aiData.size() == 0) {
			return resultMap;
		}

		// 构建批量查询SQL
		EasySQL itemSql = new EasySQL("SELECT cc_dim_date."+ statColumn +" AS QC_TIME, t1.AGENT_NAME, t2.ITEM_NAME, COUNT(1) AS COUNT");
		itemSql.append(" FROM " + getTableName("cc_qc_capacity t1"));
		itemSql.append(" INNER JOIN " + getTableName("cc_qc_details t2") + " ON t1.ID = t2.CAPACITY_ID");
		itemSql.append(" LEFT JOIN " + Constants.getStatSchema() + ".CC_DIM_DATE cc_dim_date ON DATE(t1.QC_TIME) = cc_dim_date.DATE_VALUE");
		itemSql.append(startTime," WHERE t1.QC_TIME >= ? ");
		itemSql.append(endTime," AND t1.QC_TIME <= ? ");
		if (isFatal){
			itemSql.append(" AND t1.SCORE <= 80"); //致命差错统计
		} else {
			itemSql.append(" AND t1.SCORE > 80 AND t1.SCORE <= 95"); //非致命差错统计
		}
		itemSql.append(" AND t2.SCORE < 0"); //必须是扣分项
		itemSql.append(" GROUP BY cc_dim_date."+ statColumn +", t1.AGENT_NAME, t2.ITEM_NAME");
		itemSql.append(" ORDER BY cc_dim_date."+ statColumn +", t1.AGENT_NAME");

		logger.info(CommonUtil.getClassNameAndMethod(this) + "批量查询智能质检问题统计sql:" + itemSql.getSQL() + ",参数:" + JSON.toJSONString(itemSql.getParams()));

		try {
			JSONObject batchResult = queryForList(itemSql.getSQL(), itemSql.getParams(), new JSONMapperImpl());
			JSONArray batchData = batchResult.getJSONArray("data");

			// 按时间_坐席名分组整理数据
			for(int i = 0; i < batchData.size(); i++) {
				JSONObject item = batchData.getJSONObject(i);
				String qcTime = item.getString("QC_TIME");
				String agentName = item.getString("AGENT_NAME");
				String key = qcTime + "_" + agentName;

				if(!resultMap.containsKey(key)) {
					resultMap.put(key, new JSONArray());
				}

				JSONObject itemData = new JSONObject();
				itemData.put("ITEM_NAME", item.getString("ITEM_NAME"));
				itemData.put("COUNT", item.getInteger("COUNT"));
				resultMap.get(key).add(itemData);
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "批量查询智能质检问题统计失败：" + e.getMessage(), e);
		}

		return resultMap;
	}

	/**
	 * 获取统计结果 统计致命与非致命质检项目 - 保留原方法作为备用
	 * @param agentName
	 * @param qcTime
	 * @param statColumn
	 * @return 统计sql
	 */
	@Deprecated
	private EasySQL getStatItemSqlByAi(String agentName, String qcTime, String statColumn,boolean isFata) {
		EasySQL itemSql = new EasySQL("SELECT t2.ITEM_NAME, COUNT(1)  AS COUNT");
		itemSql.append(" FROM " + getTableName("cc_qc_capacity t1"));
		itemSql.append(" LEFT JOIN " + getTableName("cc_qc_details t2") + " ON t1.ID = t2.CAPACITY_ID");
		itemSql.append(" LEFT JOIN " + Constants.getStatSchema() + ".CC_DIM_DATE cc_dim_date on DATE(t1.QC_TIME) = cc_dim_date.DATE_VALUE ");
		itemSql.append(" WHERE 1 = 1");
		if (isFata){
			itemSql.append(" AND t1.SCORE <= 80"); //致命差错统计
		} else {
			itemSql.append(" AND t1.SCORE > 80 AND t1.score <= 95 "); //非致命差错统计
		}
		itemSql.append(" AND t2.SCORE < 0"); //必须是扣分项
		itemSql.append(agentName," AND t1.AGENT_NAME = ?");
		itemSql.append(qcTime," AND cc_dim_date."+ statColumn +" = ?");
		itemSql.append(" GROUP BY t2.ITEM_NAME");
		logger.info(CommonUtil.getClassNameAndMethod(this) + "查询智能质检问题统计sql:" + itemSql.getSQL() + ",参数:" + JSON.toJSONString(itemSql.getParams()));
		return itemSql;
	}

}
