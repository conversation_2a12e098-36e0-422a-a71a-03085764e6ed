package com.yunqu.yc.quality.dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.SystemParamUtil;
import com.yunqu.yc.quality.dao.sql.QcTaskObjSql;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.yc.quality.base.AppDaoContext;
import com.yunqu.yc.quality.base.CommonLogger;
import com.yunqu.yc.quality.base.Constants;
import com.yunqu.yc.quality.cache.ThirdPartyFieldCache;
import com.yunqu.yc.quality.handler.TaskHandler;
import com.yunqu.yc.quality.utils.DateUtil;
import com.yunqu.yc.quality.utils.PhoneCryptor;
import com.yunqu.yc.quality.utils.QueryUtil;
import com.yunqu.yc.quality.utils.SQLUtil;
import com.yunqu.yc.quality.utils.StringUtil;

/***
 * 质检任务对象dao
 * <AUTHOR>
 */
@WebObject(name="QcTaskObjDao")
public class QcTaskObjDao extends AppDaoContext{
	
	private static Logger logger = CommonLogger.logger;

	/**
	 * 根据objId或人工质检结果ID获取质检对象追踪日志参数
	 */
	public static JSONObject getQcTaskObj(EasyQuery query,String schema,String objId,String rgResultId,String entId,String busiOrderId) throws SQLException {
		
		EasySQL objSql = QcTaskObjSql.getQcTaskObj(schema,objId,rgResultId,entId,busiOrderId);
		logger.info(objSql.getSQL()+""+JSON.toJSONString(objSql.getParams()));
		JSONObject taskObj = query.queryForRow(objSql.getSQL(),objSql.getParams(),new JSONMapperImpl());
		if (taskObj != null){
			String recordAgentId = taskObj.getString("RECORD_AGENTID");
			JSONObject agent = CacheUtil.getCcUserCache().getCache(taskObj.getString("ENT_ID"), taskObj.getString("BUSI_ORDER_ID"), recordAgentId);
			String agentInfo = agent.getString("USERNAME")+"("+agent.getString("USER_ACCT")+")";
			taskObj.put("AGENT",agentInfo);
			logger.info("objId【"+objId+"】的质检对象追踪日志参数:"+taskObj.toJSONString());
		}
		return taskObj;
	}
	
	/**
	 * 根据objId或人工质检结果ID获取质检对象追踪日志参数
	 */
	public static JSONObject getQcTaskObj2(String entId,String busiOrderId,EasyQuery query,String schema,String objId,String rgResultId) throws SQLException {
		EasySQL objSql = QcTaskObjSql.getQcTaskObj2(entId,busiOrderId,schema,objId,rgResultId);
		
		JSONObject taskObj = query.queryForRow(objSql.getSQL(),objSql.getParams(),new JSONMapperImpl());
		if (taskObj != null){
			String recordAgentId = taskObj.getString("RECORD_AGENTID");
			JSONObject agent = CacheUtil.getCcUserCache().getCache(taskObj.getString("ENT_ID"), taskObj.getString("BUSI_ORDER_ID"), recordAgentId);
			String agentInfo = agent.getString("USERNAME")+"("+agent.getString("USER_ACCT")+")";
			taskObj.put("AGENT",agentInfo);
			logger.info("objId【"+objId+"】的质检对象追踪日志参数:"+taskObj.toJSONString());
		}else{
			taskObj = new JSONObject();
		}
		return taskObj;
	}
	
	/**
	 * 任务对象
	 * @return
	 */
	@WebControl(name="taskObjRecord", type=Types.RECORD)
	public JSONObject taskObjRecord(){
		String id = param.getString("qcTaskObj.ID");
		
		EasySQL sql = new EasySQL();
		sql.append("SELECT t3.GROUP_LIST AGENT_GROUP_LIST,t3.ROLE_LIST AGENT_ROLE_LIST,t2.IMG_URL AGENT_IMG_URL,T1.*,CASE WHEN t1.RG_QC_SCORE IS NOT NULL THEN t1.RG_QC_SCORE ELSE t1.ZN_QC_SCORE END AS ACTUAL_SCORE FROM "+getTableName("CC_QC_TASK_OBJ T1"));
		sql.append("left join CC_USER t2 on t1.AGENT_ID = t2.USER_ID ");
		sql.append("left join "+getTableName("CC_BUSI_USER")+"  t3 on t1.AGENT_ID = t3.USER_ID AND T1.ENT_ID = T3.ENT_ID AND T3.BUSI_ORDER_ID = T1.BUSI_ORDER_ID ");
		sql.append(id,"WHERE T1.ID = ?",false);
		JSONObject taskObjRecord = queryForRecord(sql.getSQL(), sql.getParams());
		JSONObject taskObj = taskObjRecord.getJSONObject("data");
		logger.info("taskObj:" + taskObj.toJSONString());
		String znQcScore = taskObj.getString("ZN_QC_SCORE");
		logger.info("znQcScore:"+znQcScore);
		if (StringUtil.isNotBlank(znQcScore)) {
			//将 znQcScore 转化为 double类型
			double value = Double.parseDouble(znQcScore);
			logger.info("value:"+value);
			taskObj.put("ZN_QC_RESULT", value > 95 ? "1" : (value <= 80 ? "2" : "3"));
		} else {
			taskObj.put("ZN_QC_RESULT", "");
		}
		return taskObjRecord;
	}
	
	/**
	 * 质检任务列表   语音
	 * @return
	 */
	@InfAuthCheck(resId ={"cc-qc-rwgl","cc-qc-rwzx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="objVoiceList", type=Types.LIST)
	public JSONObject objVoiceList(){
		PhoneCryptor cryptor = PhoneCryptor.getInstance();
		String entId = this.getEntId();
		UserModel user = UserUtil.getUser(request);
		String dateId = param.getString("dateId");
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		logger.info("startDate:"+startDate+",endDate:"+endDate);
		if(StringUtil.isBlank(startDate) || StringUtil.isBlank(endDate)) {
			return EasyResult.fail("查找日期不能为空");
		}
		//任务数据详情页面，最大查找天数不能大于配置的天数
		int days = DateUtil.bwDays(startDate, endDate, DateUtil.TIME_FORMAT_YMD) + 1;
		int maxDays = Constants.getSearchMaxDays(request);
		if(days > maxDays) {
			return EasyResult.fail("最大查找天数不能超过" + maxDays);
		}
		String taskId = param.getString("taskId");
		String busiOrderId = getBusiOrderId();
		EasySQL sql = this.getEasySQL("select T1.CHANNEL_TYPE,t2.ORDER_NO,T1.RG_STATE AS QC_STATE,T3.ZN_CLASS_ID,T3.EXAM_GROUP_ID,T1.ID AS OBJ_ID,T1.IS_TYPEICAL,T1.LABEL_CONTENT,T2.CREATE_CAUSE,T2.AGENT_RELEASE,T2.GROUP_ID,T2.SERIAL_ID,T2.CUST_PHONE,T2.DATE_ID,T2.CASE_STATUS,T2.CLEAR_CAUSE");
		sql.append(",T2.AGENT_NAME,T2.AGENT_PHONE,T2.PHONE_NUM,T2.CALLER,T2.CALLED,T2.BILL_BEGIN_TIME,T2.BILL_END_TIME,T2.BILL_TIME,T2.SATISF_ID,t3.RG_CLASS_ID as CLASS_ID,T1.RG_RESULT_ID QC_RESULT_ID,T1.CHANNEL_TYPE CALL_TYPE, T1.RG_QC_SCORE RG_SCORE, T1.ZN_QC_SCORE ZN_SCORE, T1.CREATE_TIME, T1.RG_QC_TIME,T1.ONE_VOTE_ITEM,T1.SOURCE ");
		sql.append("FROM "+getTableName("CC_QC_TASK_OBJ T1"));
		sql.append("LEFT JOIN "+getTableName("CC_CALL_RECORD t2") +" ON T1.SERIAL_ID = T2.SERIAL_ID ");
		sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID ");
		if(StringUtils.isNotBlank(param.getString("dfOrder"))) {
			sql.append("LEFT JOIN "+getTableName("C_DFRCB_ORDER")+" T5 ON T5.SERIAL_ID = T2.SERIAL_ID ");
		}

		sql.append("WHERE 1=1 ");
		if(!param.getBooleanValue("isadmin")) {
			sql.append("AND T1.RG_STATE NOT IN ('0', '1') ");
			//抽检人
			sql.append(user.getUserAcc(), "AND T1.INSPECTOR=?");
		}
		sql.append(param.getString("source")," AND T1.SOURCE = ? ");
		sql.append(taskId,"AND T3.ID = ?",false);
		sql.append(entId, " and T1.ENT_ID = ?");
		sql.append(busiOrderId, " and T1.BUSI_ORDER_ID = ?");
		sql.append(taskId,"AND T1.TASK_ID = ?",false);
		
        sql.appendLike(param.getString("agentKey"), "and T2.AGENT_NAME like ? ");
        
        //呼入呼出参数  CREATE_CAUSE  IN(1,2,3,4,5,9,10,14) 呼入
        String callType = param.getString("callType");
        if(StringUtils.isNotBlank(callType)){
        	//呼叫创建原因 1外线呼入 2IVR转入 3席间转移呼入 4IVR咨询呼入 5席间咨询呼入 6呼出 8预拨号呼出 9呼叫前转呼入 10转移呼入 14席间呼入 29 席间呼出
        	if("01".equals(callType)){
        		sql.append(" and T2.CREATE_CAUSE IN (1,2,3,4,5,9,10,14) "); 
        	}else{
        		sql.append(" and T2.CREATE_CAUSE IN (6,8,29) "); 
        	}
        }
        
        //挂断类型   1  用户挂断  2 坐席挂断 3 系统挂断
        sql.append(param.getString("agentRelease"), "and T2.AGENT_RELEASE = ?");
        //是否典型录音 01否 02是
        sql.append(param.getString("isTypeical"), "and T1.IS_TYPEICAL = ?");
        //标注内容
        sql.appendLike(param.getString("labelContent"),"and T1.LABEL_CONTENT like ?");
        //大丰的工单类型
    	sql.append(param.getString("orderCata"),"and T5.ORDER_TYPE = ?");
    	//大丰的工单子类型
    	sql.append(param.getString("orderType"),"and T5.ORDER_SUBLEVEL_TYPE = ?");
    	
    	sql.append(entId, "AND T2.ENT_ID = ?");// T2.DATE_ID需要配合T2.ENT_ID才能使用联合索引
    	logger.info("dateId:"+dateId+",startDate:"+startDate+",endDate:"+endDate);
		//按统计时间获取数据时
		if(StringUtils.isNotBlank(dateId)){
			sql.append(dateId, " and T2.DATE_ID >= ? ");
			sql.append(dateId, " and T2.DATE_ID <= ?");
		}else{
			if(StringUtils.isNotBlank(startDate)){
				//DATE_ID为int类型 postgresql需要类型转换
				sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
			}
		}
		sql.append(param.getString("satisf"), " and T2.SATISF_ID = ?");
		//加密号码查询，不支持模糊查询
		String called = this.param.getString("called");
		String caller = this.param.getString("caller");
		if(StringUtils.isNotBlank(called)){
			sql.append("and T2.CALLED in('"+cryptor.encrypt(entId, called, cryptor.BUSI_TYPE_PHONE)+"','"+called+"')");
		}
		if(StringUtils.isNotBlank(caller)){
			sql.append("and T2.CALLER in('"+cryptor.encrypt(entId, caller, cryptor.BUSI_TYPE_PHONE)+"','"+caller+"')");
		}
		//下一条
		String isNext = this.param.getString("isNext");
		if (StringUtils.equals("Y", isNext)) {
			sql.append(2," and T1.RG_STATE = ?");
		}else{
			sql.append(param.getString("qcStatus")," and T1.RG_STATE = ?");
		}
		
		
		String oneVoteItem = param.getString("oneVoteItem");
	    if(StringUtils.isNotBlank(oneVoteItem)){
        	if("Y".equals(oneVoteItem)){
        		sql.append(" and T1.ONE_VOTE_ITEM >0"); 
        	}else if("N".equals(oneVoteItem)){
        		sql.append(" and T1.ONE_VOTE_ITEM =0 "); 
        	}
        }
		sql.append("ORDER BY T1.RG_RESULT_ID,T1.CREATE_TIME DESC ");
		
		if(ServerContext.isDebug()){
			logger.info(CommonUtil.getClassNameAndMethod(this) + "查找质检数据 >> " + sql.getSQL() + ", param >> " + JSON.toJSONString(sql.getParams()));;
		}
		
		JSONObject list = queryForPageList(sql.getSQL(), sql.getParams());
		list.put("data", cryptor.decrypt(list.getJSONArray("data"), new String[]{"CALLER","CALLED","CUST_PHONE"}, false));
		
		EasySQL sql1 = new EasySQL("select count(*) from " + getTableName("cc_qc_group_inspector") + " T1 ");
		sql1.append("where 1=1");
		sql1.append(getUserId(), "AND T1.USER_ID=?", false);
		sql1.append(taskId, "AND T1.EXAM_GROUP_ID=(select T2.EXAM_GROUP_ID from " +
				getTableName("cc_qc_task") + " T2 WHERE T2.ID=?)", false);
		try {
			boolean isInspector = false;
			isInspector = getQuery().queryForExist(sql1.getSQL(), sql1.getParams());
			list.put("isInspector", isInspector);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计质检数据错误", e);
		}
		return list;
	}
	
	
	/**
	 * 质检任务列表   全媒体
	 * @return
	 */
	@InfAuthCheck(resId ={"cc-qc-rwgl","cc-qc-rwzx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="objMediaList", type=Types.LIST)
	public JSONObject objMediaList(){
		
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		
		if(StringUtil.isBlank(startDate) || StringUtil.isBlank(endDate)) {
			return EasyResult.fail("查找日期不能为空");
		}
		UserModel user = UserUtil.getUser(request);
		//任务数据详情页面，最大查找天数不能大于配置的天数
		int days = DateUtil.bwDays(startDate, endDate, DateUtil.TIME_FORMAT_YMD) + 1;
		int maxDays = Constants.getSearchMaxDays(request);
		if(days > maxDays) {
			return EasyResult.fail("最大查找天数不能超过" + maxDays);
		}
		String taskId = param.getString("taskId");
		String entId = getEntId();
		String busiOrderId = getBusiOrderId();
		EasySQL sql = this.getEasySQL("select  T2.CHANNEL_TYPE AS MEDIA_CHANNEL_TYPE,T3.ZN_CLASS_ID,T3.EXAM_GROUP_ID,T1.ID AS OBJ_ID,T2.SERIAL_ID,T2.SESSION_ID,T2.DATE_ID,T1.CHANNEL_TYPE,T2.CUST_NAME,T2.CHANNEL_NAME,T2.KEY_NAME,T2.AGENT_PHONE,T2.CASE_STATUS");
		sql.append(",T2.AGENT_NAME,T2.REQ_TIME,T2.BEGIN_TIME,T2.END_TIME,T2.SERVER_TIME,T2.CLEAR_CAUSE,T2.SATISF_CODE,T2.SESSION_SEQ,T1.RG_STATE AS QC_STATE,T3.RG_CLASS_ID as CLASS_ID,T1.RG_RESULT_ID QC_RESULT_ID,T1.CHANNEL_TYPE CALL_TYPE,T1.RG_QC_SCORE RG_SCORE, T1.ZN_QC_SCORE ZN_SCORE, T1.CREATE_TIME,T1.RG_QC_TIME,T1.ONE_VOTE_ITEM,T1.SOURCE ");
		sql.append("FROM "+getTableName("CC_QC_TASK_OBJ T1"));
		sql.append("LEFT JOIN "+getTableName("CC_MEDIA_RECORD T2") +" ON T1.SERIAL_ID = T2.SERIAL_ID ");
		sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
		sql.append("WHERE 1=1 ");
		if(!param.getBooleanValue("isadmin")) {
			//人工抽检状态
			sql.append("AND T1.RG_STATE NOT IN ('0', '1') ");
			//抽检人
			sql.append(user.getUserAcc(), "AND T1.INSPECTOR=?");
		}
		
		sql.append(param.getString("source")," AND T1.SOURCE = ? ");
		sql.append(taskId,"AND T1.TASK_ID = ?",false);
		sql.append(taskId,"AND T3.ID = ?",false);
		sql.append(entId, " and T1.ENT_ID = ?");
		sql.append(busiOrderId, " and T1.BUSI_ORDER_ID = ?");
		sql.append(entId, "AND T2.ENT_ID = ?");// T2.DATE_ID需要配合T2.ENT_ID才能使用联合索引
		sql.append(CommonUtil.parseInteger(param.getString("channelType"))," AND T1.CHANNEL_TYPE = ? ");
		sql.appendRLike(param.getString("keyName")," AND T2.KEY_NAME like ? ");
        sql.appendLike(param.getString("agentKey")," AND T2.AGENT_NAME like ? ");
        sql.append(param.getString("MEDIA_CHANNEL_TYPE"), " and T2.CHANNEL_TYPE = ?");
        
        //呼入呼出参数  CREATE_CAUSE  IN(1,2,3,4,5,9,10,14) 呼入
        String callType = param.getString("callType");
        if(StringUtils.isNotBlank(callType)){
        	if("01".equals(callType)){
        		sql.append(" and T2.CREATE_CAUSE IN (1,2,3,4,5,9,10,14) "); 
        	}else{
        		sql.append(" and T2.CREATE_CAUSE NOT IN (6,8,29) "); 
        	}
        }
        String oneVoteItem = param.getString("oneVoteItem");
        if(StringUtils.isNotBlank(oneVoteItem)){
        	if("Y".equals(oneVoteItem)){
        		sql.append(" and T1.ONE_VOTE_ITEM >0"); 
        	}else if("N".equals(oneVoteItem)){
        		sql.append(" and T1.ONE_VOTE_ITEM =0 "); 
        	}
        }
        
		//按统计时间获取数据时
        String dateId =param.getString("dateId");
		if(StringUtils.isNotBlank(dateId)){
			sql.append(CommonUtil.parseInteger(dateId), " and T2.DATE_ID >= ? ");
			sql.append(CommonUtil.parseInteger(dateId), " and T2.DATE_ID <= ?");
		}else{
			if(StringUtils.isNotBlank(startDate)){
				sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
			}
		}
		
		sql.append(param.getString("satisf")," and T2.SATISF_CODE = ?");
		sql.append(CommonUtil.parseInteger(param.getString("sessionSeq"))," and T2.SESSION_SEQ = ?");
		
		//下一条
		String isNext = this.param.getString("isNext");
		if (StringUtils.equals("Y", isNext)) {
			sql.append(2," and T1.RG_STATE = ?");
		}else{
			sql.append(param.getString("qcStatus")," and T1.RG_STATE = ?");
		}
		
		sql.append("ORDER BY T1.RG_RESULT_ID,T1.CREATE_TIME DESC ");
		
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + "质检任务列表数据 全媒体 sql >> " + sql.getSQL() + "， param >> " + JSON.toJSONString(sql.getParams()));
		}
		
		JSONObject json = queryForPageList(sql.getSQL(), sql.getParams());
		
		EasySQL sql1 = new EasySQL("select count(*) from " + getTableName("cc_qc_group_inspector") + " T1 ");
		sql1.append("where 1=1");
		sql1.append(getUserId(), "AND T1.USER_ID=?", false);
		sql1.append(taskId, "AND T1.EXAM_GROUP_ID=(select T2.EXAM_GROUP_ID from " +
				getTableName("cc_qc_task") + " T2 WHERE T2.ID=?)", false);
		try {
			boolean isInspector = false;
			isInspector = getQuery().queryForExist(sql1.getSQL(), sql1.getParams());
			json.put("isInspector", isInspector);
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return json;
	}

	/**
	 * 质检任务列表   邮件
	 * @return
	 */
	@InfAuthCheck(resId ={"cc-qc-rwgl","cc-qc-rwzx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="objEmailList", type=Types.LIST)
	public JSONObject objEmailList(){
		
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		if(StringUtil.isBlank(startDate) || StringUtil.isBlank(endDate)) {
			return EasyResult.fail("查找日期不能为空");
		}
		UserModel user = UserUtil.getUser(request);
		//任务数据详情页面，最大查找天数不能大于配置的天数
		int days = DateUtil.bwDays(startDate, endDate, DateUtil.TIME_FORMAT_YMD) + 1;
		int maxDays = Constants.getSearchMaxDays(request);
		if(days > maxDays) {
			return EasyResult.fail("最大查找天数不能超过" + maxDays);
		}
		String taskId = param.getString("taskId");
		String entId = getEntId();
		String busiOrderId = getBusiOrderId();
		EasySQL sql = this.getEasySQL("select T1.CHANNEL_TYPE,T1.ID AS OBJ_ID,T1.SERIAL_ID,T1.RG_STATE AS QC_STATE,T1.RG_RESULT_ID QC_RESULT_ID,T1.CHANNEL_TYPE CALL_TYPE,T1.RG_QC_SCORE RG_SCORE, T1.ZN_QC_SCORE ZN_SCORE, T1.CREATE_TIME,T1.RG_QC_TIME,T1.ONE_VOTE_ITEM,T1.SOURCE");
		sql.append(",T2.DATE_ID,T2.HANDLE_ACC AGENT_ACC,T2.HANDLE_ACC_NAME AGENT_NAME,T2.EMAIL_FROM_NAME AS CUST_NAME,T2.TITLE,T2.CASE_NUM,T2.EMAIL_FROM,T2.EMAIL_TO,T2.RECE_TYPE,T2.SATISF_CODE,T2.IS_SOLVE,T2.IS_STAR,T2.FIRST_HANDLE_ACC_NAME,T2.PROCESS_SECONDS,T2.CREATE_TIME AS EMAIL_CREATE_TIME,T2.SOLVE_TIME,T2.CLOSE_TIME,T2.CLEAR_CAUSE,T2.WORK_GROUP_NAME,T2.WORK_GROUP_ID");
		sql.append(",T3.ZN_CLASS_ID,T3.EXAM_GROUP_ID,T3.RG_CLASS_ID as CLASS_ID ");
		sql.append("FROM "+getTableName("CC_QC_TASK_OBJ T1"));
		sql.append("LEFT JOIN "+getTableName("C_EMAIL_SESSION T2") +" ON T1.SERIAL_ID = T2.ID ");
		sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
		sql.append("WHERE 1=1 ");
		if(!param.getBooleanValue("isadmin")) {
			//人工抽检状态
			sql.append("AND T1.RG_STATE NOT IN ('0', '1') ");
			//抽检人
			sql.append(user.getUserAcc(), "AND T1.INSPECTOR=?");
		}
		sql.append(param.getString("source")," AND T1.SOURCE = ? ");
		sql.append(taskId,"AND T1.TASK_ID = ?",false);
		sql.append(taskId,"AND T3.ID = ?",false);
		sql.append(entId, " and T1.ENT_ID = ?");
		sql.append(busiOrderId, " and T1.BUSI_ORDER_ID = ?");


		//邮件会话表SQL条件
		sql.append(entId, "AND T2.ENT_ID = ?");// T2.DATE_ID需要配合T2.ENT_ID才能使用联合索引
		sql.appendLike(param.getString("AGENT_NAME")," AND T2.HANDLE_ACC_NAME like ? ");
		sql.appendLike(param.getString("TITLE")," AND T2.TITLE like ? ");
		sql.appendLike(param.getString("CUST_NAME")," AND T2.EMAIL_FROM_NAME like ? ");
		sql.appendLike(param.getString("EMAIL_FROM")," AND T2.EMAIL_FROM like ? ");
		sql.append(param.getString("RECE_TYPE")," AND T2.RECE_TYPE = ? ");
		sql.append(param.getString("SATISF_CODE")," AND T2.SATISF_CODE = ? ");
		sql.append(param.getString("IS_SOLVE")," AND T2.IS_SOLVE = ? ");

		//是否一票否决
		String oneVoteItem = param.getString("oneVoteItem");
		if(StringUtils.isNotBlank(oneVoteItem)){
			if("Y".equals(oneVoteItem)){
				sql.append(" and T1.ONE_VOTE_ITEM >0");
			}else if("N".equals(oneVoteItem)){
				sql.append(" and T1.ONE_VOTE_ITEM =0 ");
			}
		}

		//按统计时间获取数据时
		String dateId = param.getString("dateId");
		if(StringUtils.isNotBlank(dateId)){
			sql.append(CommonUtil.parseInteger(dateId), " and T2.DATE_ID >= ? ");
			sql.append(CommonUtil.parseInteger(dateId), " and T2.DATE_ID <= ?");
		}else{
			if(StringUtils.isNotBlank(startDate)){
				sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
			}
		}

//		sql.append(param.getString("satisf")," and T2.SATISF_CODE = ?");
//		sql.append(param.getString("sessionSeq")," and T2.SESSION_SEQ = ?");
		
		//下一条
		String isNext = this.param.getString("isNext");
		if (StringUtils.equals("Y", isNext)) {
			sql.append(2," and T1.RG_STATE = ?");
		}else{
			sql.append(param.getString("qcStatus")," and T1.RG_STATE = ?");
		}
		

		sql.append("ORDER BY T1.RG_RESULT_ID,T1.CREATE_TIME DESC ");

		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + "质检任务列表数据 邮件 sql >> " + sql.getSQL() + "， param >> " + JSON.toJSONString(sql.getParams()));
		}

		JSONObject json = queryForPageList(sql.getSQL(), sql.getParams());

		EasySQL sql1 = new EasySQL("select count(*) from " + getTableName("cc_qc_group_inspector") + " T1 ");
		sql1.append("where 1=1");
		sql1.append(getUserId(), "AND T1.USER_ID=?", false);
		sql1.append(taskId, "AND T1.EXAM_GROUP_ID=(select T2.EXAM_GROUP_ID from " +
				getTableName("cc_qc_task") + " T2 WHERE T2.ID=?)", false);
		try {
			boolean isInspector = false;
			isInspector = getQuery().queryForExist(sql1.getSQL(), sql1.getParams());
			json.put("isInspector", isInspector);
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return json;
	}
	
	/**
	 * 修改质检任务列表   全媒体
	 * 质检模块没有用到这个方法，不知道是不是其他模块调用的，先标记一下
	 * @return
	 */
	@WebControl(name="objMediaUdateList", type=Types.LIST)
	public JSONObject objMediaUdateList(){
	
		String start = param.getString("startDate");
		String startDate = "";
		String endDate ="";
		if (StringUtils.isNotBlank(start)) {
			startDate = start.substring(0,10).trim();
			endDate =start.substring(13,23).trim();
		}
		String entId = getEntId();
		String busiOrderId = getBusiOrderId();
		EasySQL sql = this.getEasySQL("select T3.ZN_CLASS_ID,T3.EXAM_GROUP_ID,T1.ID AS OBJ_ID,T2.SERIAL_ID,T2.SESSION_ID,T2.DATE_ID,T2.CHANNEL_TYPE,T2.CUST_NAME,T2.CHANNEL_NAME,T2.KEY_NAME,T2.AGENT_PHONE,T2.CASE_STATUS");
		sql.append(",T2.AGENT_NAME,T2.REQ_TIME,T2.BEGIN_TIME,T2.END_TIME,T2.SERVER_TIME,T2.CLEAR_CAUSE,T1.RG_STATE AS QC_STATE,T3.RG_CLASS_ID as CLASS_ID,T1.RG_RESULT_ID QC_RESULT_ID,T1.CHANNEL_TYPE CALL_TYPE,T1.RG_QC_SCORE RG_SCORE, T1.ZN_QC_SCORE ZN_SCORE, T1.CREATE_TIME,T1.RG_QC_TIME,T1.ONE_VOTE_ITEM ");
		sql.append("FROM "+getTableName("CC_QC_TASK_OBJ T1"));
		sql.append("LEFT JOIN "+getTableName("CC_MEDIA_RECORD T2") +" ON T1.SERIAL_ID = T2.SERIAL_ID ");
		sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
		sql.append("WHERE 1=1 ");
		sql.append(entId, " and T1.ENT_ID = ?");
		sql.append(busiOrderId, " and T1.BUSI_ORDER_ID = ?");
		sql.append(param.getString("channelType")," AND T2.CHANNEL_TYPE = ? ");
		sql.appendRLike(param.getString("keyName")," AND T2.KEY_NAME like ? ");
        sql.appendLike(param.getString("agentKey")," AND T2.AGENT_ID like ? ");
        //呼入呼出参数  CREATE_CAUSE  IN(1,2,3,4,5,9,10,14) 呼入
        String callType = param.getString("callType");
        if(StringUtils.isNotBlank(callType)){
        	if("01".equals(callType)){
        		sql.append(" and T2.CREATE_CAUSE IN (1,2,3,4,5,9,10,14) "); 
        	}else{
        		sql.append(" and T2.CREATE_CAUSE NOT IN (6,8,29) "); 
        	}
        }
        String oneVoteItem = param.getString("oneVoteItem");
        if(StringUtils.isNotBlank(oneVoteItem)){
        	if("Y".equals(oneVoteItem)){
        		sql.append(" and T1.ONE_VOTE_ITEM >0"); 
        	}else if("N".equals(oneVoteItem)){
        		sql.append(" and T1.ONE_VOTE_ITEM =0 "); 
        	}
        }
		//按统计时间获取数据时
    	String dateId = param.getString("dateId");
		if(StringUtils.isNotBlank(dateId)){
			sql.append(CommonUtil.parseInteger(dateId), " and T2.DATE_ID >= ? ");
			sql.append(CommonUtil.parseInteger(dateId), " and T2.DATE_ID <= ?");
		}else{
			if(StringUtils.isNotBlank(startDate)){
				sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
			}
		}
        sql.append(param.getString("rgState")," and T1.RG_STATE= ? ");
		sql.append("ORDER BY T1.RG_RESULT_ID,T1.CREATE_TIME DESC ");
		JSONObject json = queryForPageList(sql.getSQL(), sql.getParams());
		return json;
	}
	
	
	
	
	/**
	 * 智能得分详情
	 */
	@WebControl(name="qcDetailsList", type=Types.TEMPLATE)
	public JSONObject qcDetailsList(){
		String serialId = param.getString("serialId");
//		serialId = "84244830436379202425249";
		EasySQL sql = this.getEasySQL("");
		sql.append("SELECT T1.* FROM "+getTableName("CC_QC_DETAILS")+" T1");
		sql.append("LEFT JOIN  "+getTableName("CC_QC_CAPACITY")+" T2 ON T1.CAPACITY_ID = T2.ID");
		sql.append("WHERE 1=1 ");
		sql.append(serialId,"AND T2.SERIAL_ID = ? ",false);
		sql.append(param.getString("znClassId"),"AND T2.ZN_CLASS_ID = ? ",false);
		sql.append(" AND T1.SCORE < 0 ");//只筛选扣分项
		sql.append("ORDER BY T1.ITEMNAME_ID");
		JSONObject queryForList = this.queryForList(sql.getSQL(), sql.getParams());
		return queryForList;
	}
	
	/**
	 * 质检任务列表   语音
	 * @return
	 */
	@InfAuthCheck(resId ={"cc-qc-rwgl","cc-qc-rwzx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="readyingList", type=Types.LIST)
	public JSONObject readyingList(){
		PhoneCryptor cryptor = PhoneCryptor.getInstance();
		String entId = this.getEntId();
		EasySQL sql = new EasySQL("");
		UserModel user = UserUtil.getUser(request);
		sql.append("SELECT T1.ID, T1.INSPECTOR,T1.INSPECTOR_TIME,T3.ZN_CLASS_ID,T5.QC_RESULT_ID, T2.* FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
		sql.append("LEFT JOIN  " + getTableName("CC_CALL_RECORD") + " T2 ON T1.SERIAL_ID = T2.SERIAL_ID ");
		sql.append("LEFT JOIN  " + getTableName("CC_QC_TASK") + " T3 ON T1.TASK_ID = T3.ID ");
		sql.append("LEFT JOIN "+getTableName("V_CC_USER")+" T4 ON T4.USER_ID = T2.AGENT_ID AND T4.ENT_ID=T2.ENT_ID AND T4.BUSI_ORDER_ID=T2.BUSI_ORDER_ID");
		sql.append("LEFT JOIN  " + getTableName("cc_qc_result") + " T5 ON T1.RG_RESULT_ID = T5.QC_RESULT_ID ");
		sql.append("WHERE 1=1 ");
		sql.append(param.getString("taskId"), "AND T1.TASK_ID=?", false);
		sql.append("AND T1.RG_STATE in('2', '3') ");
		sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
		sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
		sql.append(user.getEpCode(), "AND T4.ENT_ID=?");
		sql.append(user.getBusiOrderId(), "AND T4.BUSI_ORDER_ID=?");
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		if(StringUtils.isNotBlank(startDate)){
			//DATE_ID为int类型 postgresql需要类型转换
			sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
		}
		
		String caller = param.getString("caller");
		String called = param.getString("called");
		
		if(StringUtils.isNotBlank(caller)){
			sql.append("and T2.caller in('"+cryptor.encrypt(entId, caller, cryptor.BUSI_TYPE_PHONE)+"','"+caller+"') ");
		}
		if(StringUtils.isNotBlank(called)){
			sql.append("and T2.called in('"+cryptor.encrypt(entId, called, cryptor.BUSI_TYPE_PHONE)+"','"+called+"') ");
		}
		
		sql.appendLike(param.getString("agentKey"), "AND T4.USER_ACC like ?");
		sql.appendLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
		sql.append("ORDER BY T1.CREATE_TIME DESC");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	
	/**
	 * 质检任务列表   全媒体
	 * @return
	 */
	@InfAuthCheck(resId ={"cc-qc-rwgl","cc-qc-rwzx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="readyingMediaList", type=Types.LIST)
	public JSONObject readyingMediaList(){
		EasySQL sql = this.getEasySQL("");
		UserModel user = UserUtil.getUser(request);
		sql.append("SELECT T1.ID, T1.INSPECTOR,T1.INSPECTOR_TIME,T3.ZN_CLASS_ID,T4.QC_RESULT_ID, T2.* FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
		sql.append("LEFT JOIN  " + getTableName("CC_MEDIA_RECORD") + " T2 ON T1.SERIAL_ID = T2.SERIAL_ID ");
		sql.append("LEFT JOIN  " + getTableName("CC_QC_TASK") + " T3 ON T1.TASK_ID = T3.ID ");
		sql.append("LEFT JOIN  " + getTableName("cc_qc_result") +" T4 ON T1.RG_RESULT_ID = T4.QC_RESULT_ID ");
		sql.append("WHERE 1=1 ");
		sql.append(param.getString("taskId"), "AND T1.TASK_ID=?", false);
		sql.append("2","AND T1.RG_STATE=? ");
		sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
		sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
		String start = param.getString("startDate");
		String startDate = "";
		String endDate ="";
		if (StringUtils.isNotBlank(start)) {
			startDate = start.substring(0,10).trim();
			endDate =start.substring(13,23).trim();
		}else {
			//兼容新版本
			startDate=param.getString("beginStartDate");
			endDate=param.getString("endStartDate");
			
		}
		if(StringUtils.isNotBlank(startDate)){
			sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
		}
		sql.appendRLike(param.getString("caller"), "AND T2.caller like ?");
		sql.appendRLike(param.getString("called"), "AND T2.called like ?");
		sql.appendLike(param.getString("agentKey"), "AND T2.AGENT_ID like ?");
		sql.appendLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
		sql.append("ORDER BY T1.CREATE_TIME DESC");
		logger.info("sql:"+sql.getSQL()+",param:"+JSON.toJSONString(sql.getParams()));
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 质检任务列表   邮件
	 * @return
	 */
	@InfAuthCheck(resId ={"cc-qc-rwgl","cc-qc-rwzx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="readyingEmailList", type=Types.LIST)
	public JSONObject readyingEmailList(){
		EasySQL sql = this.getEasySQL("");
		UserModel user = UserUtil.getUser(request);
		sql.append("SELECT T4.QC_RESULT_ID,T1.ID, T1.INSPECTOR,T1.INSPECTOR_TIME,T3.ZN_CLASS_ID ");
		sql.append(", T2.ID AS SERIAL_ID ,T2.EMAIL_FROM_NAME,T2.EMAIL_FROM,T2.EMAIL_TO,T2.RECE_TYPE,T2.TITLE,T2.CASE_NUM,T2.ORDER_ID,T2.SATISF_CODE,T2.SATISF_TIME,T2.SATISF_QS_RESULT_ID,T2.FIRST_REPLY_TIME,T2.PROCESS_SECONDS,T2.STATUS ");
		sql.append(",T2.CREATE_TIME,T2.FIRST_DISPATCH_TIME,T2.SOLVE_TIME,T2.CLOSE_TIME,T2.HANDLE_ACC,T2.HANDLE_ACC_NAME,T2.HANDLE_TIME,T2.HANDLE_DEPT_CODE,T2.IS_STAR,T2.CLEAR_CAUSE,T2.DATE_ID,T2.WORK_GROUP_ID,T2.WORK_GROUP_NAME,T2.IS_SOLVE,T2.QC_STATE");
		sql.append("FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
		sql.append("LEFT JOIN  " + getTableName("C_EMAIL_SESSION") + " T2 ON T1.SERIAL_ID = T2.ID ");
		sql.append("LEFT JOIN  " + getTableName("CC_QC_TASK") + " T3 ON T1.TASK_ID = T3.ID ");
		sql.append("LEFT JOIN  " + getTableName("cc_qc_result") + " T4 ON T1.RG_RESULT_ID = T4.QC_RESULT_ID ");
		sql.append("WHERE 1=1 ");
		sql.append(param.getString("taskId"), "AND T1.TASK_ID=?", false);
		sql.append("2","AND T1.RG_STATE=? ");
		sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
		sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		if(StringUtils.isNotBlank(startDate)){
			sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
		}
		sql.appendLike(param.getString("EMAIL_FROM"), "AND T2.EMAIL_FROM like ?");
		sql.appendLike(param.getString("EMAIL_TO"), "AND T2.EMAIL_TO like ?");
		sql.appendLike(param.getString("agentKey"), "AND T2.HANDLE_ACC like ?");
		sql.appendLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
		sql.append("ORDER BY T1.CREATE_TIME DESC");
		logger.info(CommonUtil.getClassNameAndMethod(this) + "查询sql >> " + sql.getSQL() + ", param >> " + JSON.toJSONString(sql.getParams()));;
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	
	
	/**
	 * 质检任务列表   第三方
	 * @return
	 */
	@InfAuthCheck(resId ={"cc-qc-rwgl","cc-qc-rwzx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="readyingThirdList", type=Types.LIST)
	public JSONObject readyingThirdList(){
		EasySQL sql = this.getEasySQL("");
		UserModel user = UserUtil.getUser(request);
		sql.append("SELECT T1.ID, T1.INSPECTOR,T1.INSPECTOR_TIME,T3.ZN_CLASS_ID, ");
		
		
		ThirdPartyFieldCache thirdFieldCache = new ThirdPartyFieldCache();		
		JSONObject fieldCache = thirdFieldCache.getCache(this.getEntId(), this.getBusiOrderId(), param.getString("TEMPLATE_ID"));		
		JSONArray queryForList = fieldCache.getJSONArray("showList");
		for(int i=0;i<queryForList.size();i++){
			JSONObject fieldObject = queryForList.getJSONObject(i);	
			sql.append("T2.");
			if(i==queryForList.size()-1){
				sql.append(fieldObject.getString("FIELD_EN"));
			}else{
				sql.append(fieldObject.getString("FIELD_EN")+", ");
			}		
		}	
		
		sql.append(" FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
		
		
		sql.append("LEFT JOIN  " + getTableName("cc_qc_third_record") + " T2 ON T1.SERIAL_ID = T2.ID ");
		sql.append("LEFT JOIN  " + getTableName("CC_QC_TASK") + " T3 ON T1.TASK_ID = T3.ID ");
		sql.append("WHERE 1=1 ");
		sql.append(param.getString("taskId"), "AND T1.TASK_ID=?", false);
		sql.append("2","AND T1.RG_STATE=? ");
		sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
		sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
		sql.appendLike(param.getString("agentKey"), "AND T2.AGENT_ID like ?");
		sql.appendLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
		sql = initBasicSearch("t2.",sql,param);
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 质检任务列表  工单
	 * @return
	 */
	@InfAuthCheck(resId ={"cc-qc-rwgl","cc-qc-rwzx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="readyingOrderList", type=Types.LIST)
	public JSONObject readyingOrderList(){
		JSONObject queryForPageList = null;
		try {
			EasySQL sql = this.getEasySQL("");
			UserModel user = UserUtil.getUser(request);
			sql.append("SELECT T1.ID,t1.INSPECTOR_NAME,t3.EXAM_GROUP_ID,T1.TASK_ID,T1.ID as OBJ_ID, T1.INSPECTOR,T1.INSPECTOR_TIME,T3.ZN_CLASS_ID ");
			sql.append(",t2.ORDER_NO,t2.CREATE_ACC,t2.CREATE_NAME,t2.CREATE_TIME,t2.END_TIME");
			sql.append(",t2.ORDER_LEVEL,t2.PROVINCE,t2.CITY,t2.AREA,t2.SOURCE_TYPE,t2.CUIBAN_NUM");
			sql.append(" FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
			sql.append("LEFT JOIN "+getTableName("C_BO_BASE_ORDER t2") +" ON T1.SERIAL_ID = T2.ID ");
			sql.append("LEFT JOIN  " + getTableName("CC_QC_TASK") + " T3 ON T1.TASK_ID = T3.ID ");
			sql.append("WHERE 1=1 AND T2.ID IS NOT NULL ");
			sql.append(param.getString("taskId"), "AND T1.TASK_ID=?", false);
			sql.append("2","AND T1.RG_STATE=? ");
			sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
			sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
			sql.appendLike(param.getString("agentKey"), "AND T2.AGENT_ID like ?");
			sql.appendLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
			
			sql.append("UNION ALL ");
			
			sql.append("SELECT T1.ID,t1.INSPECTOR_NAME,t3.EXAM_GROUP_ID,T1.TASK_ID,T1.ID as OBJ_ID, T1.INSPECTOR,T1.INSPECTOR_TIME,T3.ZN_CLASS_ID ");
			sql.append(",t2.ORDER_NO,t2.CREATE_ACC,t2.CREATE_NAME,t2.CREATE_TIME,t2.END_TIME");
			sql.append(",t2.ORDER_LEVEL,t2.PROVINCE,t2.CITY,t2.AREA,t2.SOURCE_TYPE,t2.CUIBAN_NUM");
			sql.append(" FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
			sql.append("LEFT JOIN "+getTableName("C_BO_BASE_ORDER_HIS t2") +" ON T1.SERIAL_ID = T2.ID ");
			sql.append("LEFT JOIN  " + getTableName("CC_QC_TASK") + " T3 ON T1.TASK_ID = T3.ID ");
			sql.append("WHERE 1=1 AND T2.ID IS NOT NULL ");
			sql.append(param.getString("taskId"), "AND T1.TASK_ID=?", false);
			sql.append("2","AND T1.RG_STATE=? ");
			sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
			sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
			sql.appendLike(param.getString("agentKey"), "AND T2.AGENT_ID like ?");
			sql.appendLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
			
			
//			sql.append(" order by CREATE_TIME desc");
			sql.append("ORDER BY CREATE_TIME DESC");
			if(ServerContext.isDebug()){
				logger.info(CommonUtil.getClassNameAndMethod(this) + "查询sql >> " + sql.getSQL() + ", param >> " + JSON.toJSONString(sql.getParams()));;
			}
			queryForPageList = queryForPageList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		
		return queryForPageList;
	}

	  private static EasySQL initBasicSearch(String tableQ,EasySQL sql,JSONObject param){   
	      SQLUtil.extendQuery(tableQ,param, sql); 
	      sql.append(" order by "+tableQ+"CREATE_TIME desc");
	      return sql;
	  }

	
	
	/**
	 * 待发布列表
	 * @return
	 */
	@InfAuthCheck(resId ={"cc-qc-rwgl","cc-qc-rwzx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="changeMediaList", type=Types.LIST)
	public JSONObject changeMediaList(){
		PhoneCryptor cryptor = PhoneCryptor.getInstance();
		String entId = this.getEntId();
		EasySQL sql = this.getEasySQL("");
		UserModel user = UserUtil.getUser(request);
		String startDate = param.getString("beginStartDate");
		String endDate =param.getString("endStartDate");
		
		String startQcTime = param.getString("beginQcTime");
		String endQcTime = param.getString("endQcTime");
		boolean startDateBlank  = StringUtil.isBlank(startDate) || StringUtil.isBlank(endDate);
		boolean qcTimeBlank  = StringUtil.isBlank(startQcTime) || StringUtil.isBlank(endQcTime);
		if(startDateBlank && qcTimeBlank) {
			return EasyResult.fail("查找日期不能为空");
		}
		String agentKey = param.getString("agentKey");
		String inspectorKey = param.getString("inspectorKey");
		String channelType = param.getString("channelType");
		//语音
		if(StringUtil.equals("1", channelType)) {
			sql.append("select T1.ID AS OBJ_ID,T3.TEMPLATE_ID,T3.RG_CLASS_ID,T1.ID,T1.CHANNEL_TYPE,T1.CHANNEL_TYPE CALL_TYPE,'语音' CHANNEL,T1.RG_RESULT_ID QC_RESULT_ID,T3.TASK_NAME,T3.EXAM_GROUP_ID,T3.ZN_CLASS_ID,T1.INSPECTOR,T1.INSPECTOR_NAME,T2.CASE_STATUS,T2.SERIAL_ID,T2.DATE_ID,T2.CUST_PHONE as CUST_NAME"
					+ ",T2.BILL_BEGIN_TIME AS BEGIN_TIME,T2.BILL_END_TIME AS END_TIME,T2.AGENT_NAME,'2' AS TYPE,T1.INSPECTOR_TIME,T1.RG_RESULT_ID,T1.CREATE_TIME, T2.BILL_TIME SERVER_TIME,T1.AGENT_ACC,T1.RG_QC_TIME,T1.TASK_ID,T1.ZN_QC_SCORE,T1.RG_QC_SCORE,T4.EVALUATE,T4.EVALUATE2 ");
			sql.append("FROM "+getTableName("CC_QC_TASK_OBJ T1"));
			sql.append("LEFT JOIN "+getTableName("CC_CALL_RECORD T2") +" ON T1.SERIAL_ID = T2.SERIAL_ID ");
			sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
			sql.append("LEFT JOIN "+getTableName("CC_QC_RESULT")+" T4 ON T4.QC_RESULT_ID = T1.RG_RESULT_ID");
			sql.append("WHERE 1=1 ");
			sql.append(param.getString("agentName")," and T1.AGENT_NAME=? ");
			sql.append(param.getString("rgState")," and T1.RG_STATE=? ");
			sql.append(user.getEpCode(), " and T1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and T1.BUSI_ORDER_ID = ?");
			sql.append("1"," AND T1.CHANNEL_TYPE= ? ");
			sql.append(param.getString("taskId")," AND T1.TASK_ID= ? ");
			//服务开始时间
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate + " 00:00:00", " and T1.SERVICE_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate + " 23:59:59", " and T1.SERVICE_TIME <=  ?");
			}
			
			if(StringUtils.isNotBlank(startQcTime)){
				sql.append(startQcTime, " and T1.RG_QC_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endQcTime)){
				sql.append(endQcTime  , " and T1.RG_QC_TIME <= ?");
			}
			sql.appendLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
			
			sql.append(user.getEpCode(), " and T2.ENT_ID = ?");
			if(StringUtils.isNotBlank(startDate)){
				sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
			}
			if(CommonUtil.isNotBlank(agentKey)) {
				sql.appendRLike(agentKey, "AND (T1.AGENT_ACC like ?");
				sql.appendRLike(agentKey," OR T2.AGENT_NAME like ? )");
			}
			if(CommonUtil.isNotBlank(inspectorKey)) {
				sql.appendRLike(inspectorKey, "AND (T1.INSPECTOR like ?");
				sql.appendRLike(inspectorKey," OR T1.INSPECTOR_NAME like ? )");
			}
			sql.append("ORDER BY T1.RG_RESULT_ID,T1.CREATE_TIME DESC");
		}
		//全媒体
		else if(StringUtil.equals("2", channelType)){
			sql.append("SELECT T1.ID AS OBJ_ID,T3.TEMPLATE_ID,T3.RG_CLASS_ID,T1.ID,T1.CHANNEL_TYPE,T1.CHANNEL_TYPE CALL_TYPE,T2.CHANNEL_NAME CHANNEL,T1.RG_RESULT_ID QC_RESULT_ID,T3.TASK_NAME,T3.EXAM_GROUP_ID,T3.ZN_CLASS_ID, T1.INSPECTOR,T1.INSPECTOR_NAME,T2.CASE_STATUS,T2.SERIAL_ID,T2.DATE_ID"
					+ ",T2.CUST_NAME,T2.BEGIN_TIME,T2.END_TIME,T2.AGENT_NAME,'1' AS TYPE,T1.INSPECTOR_TIME,T1.RG_RESULT_ID,T1.CREATE_TIME,T2.SERVER_TIME,T1.AGENT_ACC,T1.RG_QC_TIME,T1.TASK_ID,T1.ZN_QC_SCORE,T1.RG_QC_SCORE,T4.EVALUATE,T4.EVALUATE2 FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
			sql.append("LEFT JOIN  " + getTableName("CC_MEDIA_RECORD") + " T2 ON T1.SERIAL_ID = T2.SERIAL_ID ");
			sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
			sql.append("LEFT JOIN "+getTableName("CC_QC_RESULT")+" T4 ON T4.QC_RESULT_ID = T1.RG_RESULT_ID");
			sql.append("WHERE 1=1 ");
			sql.append(param.getString("agentName")," and T1.AGENT_NAME=? ");
			sql.append(param.getString("rgState"),"AND T1.RG_STATE= ? ");
			sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
			sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
			sql.append("2"," AND T1.CHANNEL_TYPE= ? ");
			sql.append(param.getString("taskId")," AND T1.TASK_ID= ? ");
			//服务开始时间
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate + " 00:00:00", " and T1.SERVICE_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate + " 23:59:59", " and T1.SERVICE_TIME <=  ?");
			}
			sql.append(user.getEpCode(), " and T2.ENT_ID = ?");
			if(StringUtils.isNotBlank(startDate)){
				sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
			}
			if(StringUtils.isNotBlank(startQcTime)){
				sql.append(startQcTime, " and T1.RG_QC_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endQcTime)){
				sql.append(endQcTime  , " and T1.RG_QC_TIME <= ?");
			}
			sql.appendRLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
			if(CommonUtil.isNotBlank(agentKey)) {
				sql.appendRLike(param.getString("agentKey"), "AND (T1.AGENT_ACC like ?");
				sql.appendRLike(param.getString("agentKey")," OR T2.AGENT_NAME like ? )");
			}
			if(CommonUtil.isNotBlank(inspectorKey)) {
				sql.appendRLike(inspectorKey, "AND (T1.INSPECTOR like ?");
				sql.appendRLike(inspectorKey," OR T1.INSPECTOR_NAME like ? )");
			}
			sql.append("ORDER BY T1.RG_RESULT_ID,T1.CREATE_TIME DESC");
		}
		//邮件
		else if(StringUtil.equals("3", channelType)){
			sql.append("SELECT T1.ID AS OBJ_ID,T3.TEMPLATE_ID,T3.RG_CLASS_ID,T1.ID,T2.ID SERIAL_ID,T1.CHANNEL_TYPE,T1.CHANNEL_TYPE CALL_TYPE,'邮件' CHANNEL,T1.RG_RESULT_ID QC_RESULT_ID,T3.TASK_NAME,T3.EXAM_GROUP_ID,T3.ZN_CLASS_ID, T1.INSPECTOR,T1.INSPECTOR_NAME,''CASE_STATUS,T2.DATE_ID"
					+ ",T2.EMAIL_FROM_NAME CUST_NAME,T2.CREATE_TIME BEGIN_TIME,T2.CLOSE_TIME END_TIME,T2.HANDLE_ACC_NAME AGENT_NAME,'1' AS TYPE,T1.INSPECTOR_TIME,T1.RG_RESULT_ID,T1.CREATE_TIME,''SERVER_TIME,T1.AGENT_ACC,T1.RG_QC_TIME,T1.TASK_ID,T1.ZN_QC_SCORE,T1.RG_QC_SCORE,T4.EVALUATE,T4.EVALUATE2 FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
			sql.append("LEFT JOIN  " + getTableName("c_email_session") + " T2 ON T1.SERIAL_ID = T2.ID ");
			sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
			sql.append("LEFT JOIN "+getTableName("CC_QC_RESULT")+" T4 ON T4.QC_RESULT_ID = T1.RG_RESULT_ID");
			sql.append("WHERE 1=1 ");
			sql.append(param.getString("agentName")," and T1.AGENT_NAME=? ");
			sql.append(param.getString("rgState"),"AND T1.RG_STATE= ? ");
			sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
			sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
			sql.append("3"," AND T1.CHANNEL_TYPE= ? ");
			sql.append(param.getString("taskId")," AND T1.TASK_ID= ? ");
			//服务开始时间
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate + " 00:00:00", " and T1.SERVICE_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate + " 23:59:59", " and T1.SERVICE_TIME <=  ?");
			}
			sql.append(user.getEpCode(), " and T2.ENT_ID = ?");
			if(StringUtils.isNotBlank(startDate)){
				sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
			}
			if(StringUtils.isNotBlank(startQcTime)){
				sql.append(startQcTime, " and T1.RG_QC_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endQcTime)){
				sql.append(endQcTime , " and T1.RG_QC_TIME <= ?");
			}
			sql.appendRLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
			if(CommonUtil.isNotBlank(agentKey)) {
				sql.appendRLike(param.getString("agentKey"), "AND (T1.AGENT_ACC like ?");
				sql.appendRLike(param.getString("agentKey")," OR T2.HANDLE_ACC like ? )");
			}
			if(CommonUtil.isNotBlank(inspectorKey)) {
				sql.appendRLike(inspectorKey, "AND (T1.INSPECTOR like ?");
				sql.appendRLike(inspectorKey," OR T1.INSPECTOR_NAME like ? )");
			}
			sql.append("ORDER BY T1.RG_RESULT_ID,T1.CREATE_TIME DESC");
		}
		//第三方
		else if(StringUtil.equals("9", channelType)){
			sql.append("SELECT T1.ID AS OBJ_ID,T3.TEMPLATE_ID,T3.RG_CLASS_ID,T1.ID,T1.CHANNEL_TYPE,T1.CHANNEL_TYPE CALL_TYPE,'自定义' as CHANNEL,T1.RG_RESULT_ID QC_RESULT_ID,T3.TASK_NAME,T3.EXAM_GROUP_ID,T3.ZN_CLASS_ID, T1.INSPECTOR,T1.INSPECTOR_NAME,T2.SERIAL_ID,T2.DATE_ID"
					+ ",T2.CALLER,T2.BEGIN_TIME,T2.END_TIME,T2.AGENT_NAME,'1' AS TYPE,T1.INSPECTOR_TIME,T1.RG_RESULT_ID,T1.CREATE_TIME,T2.BEGIN_TIME SERVER_TIME,T1.AGENT_ACC,T1.RG_QC_TIME,T1.TASK_ID,T1.ZN_QC_SCORE,T1.RG_QC_SCORE,T4.EVALUATE,T4.EVALUATE2,T2.CALLER CUST_NAME FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
			sql.append("LEFT JOIN  " + getTableName("cc_qc_third_record") + " T2 ON T1.SERIAL_ID = T2.ID ");
			sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
			sql.append("LEFT JOIN "+getTableName("CC_QC_RESULT")+" T4 ON T4.QC_RESULT_ID = T1.RG_RESULT_ID");
			sql.append("WHERE 1=1 ");
			sql.append(param.getString("agentName")," and T1.AGENT_NAME=? ");
			sql.append(param.getString("rgState"),"AND T1.RG_STATE= ? ");
			sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
			sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
			sql.append("9"," AND T1.CHANNEL_TYPE= ? ");
			sql.append(param.getString("taskId")," AND T1.TASK_ID= ? ");
			//服务开始时间
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate + " 00:00:00", " and T1.SERVICE_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate + " 23:59:59", " and T1.SERVICE_TIME <=  ?");
			}
			sql.append(user.getEpCode(), " and T2.ENT_ID = ?");
			if(StringUtils.isNotBlank(startDate)){
				sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
			}
			if(StringUtils.isNotBlank(startQcTime)){
				sql.append(startQcTime, " and T1.RG_QC_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endQcTime)){
				sql.append(endQcTime  , " and T1.RG_QC_TIME <= ?");
			}
			sql.appendRLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
			if(CommonUtil.isNotBlank(agentKey)) {
				sql.appendRLike(param.getString("agentKey"), "AND (T1.AGENT_ACC like ?");
				sql.appendRLike(param.getString("agentKey")," OR T2.AGENT_ACC like ? )");
			}
			if(CommonUtil.isNotBlank(inspectorKey)) {
				sql.appendRLike(inspectorKey, "AND (T1.INSPECTOR like ?");
				sql.appendRLike(inspectorKey," OR T1.INSPECTOR_NAME like ? )");
			}
			sql.append("ORDER BY T1.RG_RESULT_ID,T1.CREATE_TIME DESC");
		}
		//工单
		else if(StringUtil.equals("4", channelType)){
			sql.append("SELECT T1.ID AS OBJ_ID,T3.TEMPLATE_ID,T3.RG_CLASS_ID,t2.CALLER CUST_NAME,'工单' CHANNEL,T1.ID, T1.CHANNEL_TYPE, T1.CHANNEL_TYPE CALL_TYPE, T1.RG_RESULT_ID QC_RESULT_ID, T3.TASK_NAME, T3.EXAM_GROUP_ID, T3.ZN_CLASS_ID, T1.INSPECTOR,");
			sql.append("T1.INSPECTOR_NAME, T1.SERIAL_ID,t2.CREATE_TIME as BEGIN_TIME,  T2.END_TIME, T1.AGENT_NAME, '4' AS TYPE, T1.INSPECTOR_TIME, T1.RG_RESULT_ID, T1.CREATE_TIME, ");
			sql.append("T1.AGENT_ACC, T1.RG_QC_TIME, T1.TASK_ID, T1.ZN_QC_SCORE, T1.RG_QC_SCORE, T4.EVALUATE, T4.EVALUATE2  ");
			sql.append("FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
			sql.append("LEFT JOIN  " + getTableName("C_BO_BASE_ORDER") + " T2 ON T1.SERIAL_ID = T2.ID ");
			sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
			sql.append("LEFT JOIN "+getTableName("CC_QC_RESULT")+" T4 ON T4.QC_RESULT_ID = T1.RG_RESULT_ID");
			sql.append("WHERE 1 = 1 and t2.id is not null ");
			sql.append(channelType," AND T1.CHANNEL_TYPE= ? ");
			sql.append(param.getString("agentName")," and T1.AGENT_NAME=? ");
			sql.append(param.getString("rgState"),"AND T1.RG_STATE= ? ");
			sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
			sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
			sql.append(param.getString("taskId")," AND T1.TASK_ID= ? ");
			//服务开始时间
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate + " 00:00:00", " and T2.END_TIME  >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate + " 23:59:59", " and T2.END_TIME <=  ?");
			}
			
			if(StringUtils.isNotBlank(startQcTime)){
				sql.append(startQcTime, " and T1.RG_QC_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endQcTime)){
				sql.append(endQcTime  , " and T1.RG_QC_TIME <= ?");
			}
			
			
			sql.appendRLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
			if(CommonUtil.isNotBlank(agentKey)) {
				sql.appendRLike(param.getString("agentKey"), "AND (T1.AGENT_ACC like ?");
				sql.appendRLike(param.getString("agentKey")," OR T1.AGENT_NAME like ? )");
			}
			if(CommonUtil.isNotBlank(inspectorKey)) {
				sql.appendRLike(inspectorKey, "AND (T1.INSPECTOR like ?");
				sql.appendRLike(inspectorKey," OR T1.INSPECTOR_NAME like ? )");
			}
			
			sql.append("UNION ALL");
			
			sql.append("SELECT T1.ID AS OBJ_ID,T3.TEMPLATE_ID,T3.RG_CLASS_ID,t2.CALLER CUST_NAME,'工单' CHANNEL,T1.ID, T1.CHANNEL_TYPE, T1.CHANNEL_TYPE CALL_TYPE, T1.RG_RESULT_ID QC_RESULT_ID, T3.TASK_NAME, T3.EXAM_GROUP_ID, T3.ZN_CLASS_ID, T1.INSPECTOR,");
			sql.append("T1.INSPECTOR_NAME, T1.SERIAL_ID,t2.CREATE_TIME as BEGIN_TIME,  T2.END_TIME, T1.AGENT_NAME, '4' AS TYPE, T1.INSPECTOR_TIME, T1.RG_RESULT_ID, T1.CREATE_TIME, ");
			sql.append("T1.AGENT_ACC, T1.RG_QC_TIME, T1.TASK_ID, T1.ZN_QC_SCORE, T1.RG_QC_SCORE, T4.EVALUATE, T4.EVALUATE2  ");
			sql.append("FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
			sql.append("LEFT JOIN  " + getTableName("C_BO_BASE_ORDER_HIS") + " T2 ON T1.SERIAL_ID = T2.ID ");
			sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
			sql.append("LEFT JOIN "+getTableName("CC_QC_RESULT")+" T4 ON T4.QC_RESULT_ID = T1.RG_RESULT_ID");
			sql.append("WHERE 1 = 1 and t2.id is not null ");
			
			sql.append(channelType," AND T1.CHANNEL_TYPE= ? ");
			sql.append(param.getString("agentName")," and T1.AGENT_NAME=? ");
			sql.append(param.getString("rgState"),"AND T1.RG_STATE= ? ");
			sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
			sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
			sql.append(param.getString("taskId")," AND T1.TASK_ID= ? ");
			//服务开始时间
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate + " 00:00:00", " and T2.END_TIME  >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate + " 23:59:59", " and T2.END_TIME <=  ?");
			}
			
			if(StringUtils.isNotBlank(startQcTime)){
				sql.append(startQcTime, " and T1.RG_QC_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endQcTime)){
				sql.append(endQcTime  , " and T1.RG_QC_TIME <= ?");
			}
			
			
			sql.appendRLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
			if(CommonUtil.isNotBlank(agentKey)) {
				sql.appendRLike(param.getString("agentKey"), "AND (T1.AGENT_ACC like ?");
				sql.appendRLike(param.getString("agentKey")," OR T1.AGENT_NAME like ? )");
			}
			if(CommonUtil.isNotBlank(inspectorKey)) {
				sql.appendRLike(inspectorKey, "AND (T1.INSPECTOR like ?");
				sql.appendRLike(inspectorKey," OR T1.INSPECTOR_NAME like ? )");
			}
			
			
			
			
			
			sql.append("ORDER BY  RG_RESULT_ID, CREATE_TIME DESC");
		}
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + "查找质检数据 sql >> " + sql.getSQL() + ", param >> " + JSON.toJSONString(sql.getParams()));
		}
		JSONObject list = queryForPageList(sql.getSQL(), sql.getParams());
		if (StringUtil.equals("1", channelType) && list != null) {
			list.put("data", cryptor.decrypt(list.getJSONArray("data"), new String[]{"CUST_NAME"}, false));
		}
		
		return list;
	}
	
	/**
	 * 质检任务列表   全媒体
	 * @return
	 */
	@WebControl(name="statTaskObj", type=Types.LIST)
	public JSONObject statTaskObj(){
		EasySQL sql = this.getEasySQL("");
		UserModel user = UserUtil.getUser(request);
		EasyQuery query=getQuery();
		try {
			//查任务总数
			sql = this.getEasySQL("select count(*)  ");
			sql.append(" FROM "+getTableName("CC_QC_TASK T1"));
			sql.append(" WHERE 1=1 ");	
			sql.append(user.getEpCode(), " AND T1.ENT_ID=?");
			sql.append(user.getBusiOrderId(), " AND T1.BUSI_ORDER_ID=?");
			int total=query.queryForInt(sql.getSQL(), sql.getParams());
			
			sql = this.getEasySQL("");
			sql.append("SELECT T1.RG_STATE, count(1) RG_STATE_NUM");
			sql.append("FROM "+getTableName("CC_QC_TASK_OBJ T1"));
			sql.append("WHERE 1=1 ");
			sql.append("AND T1.RG_STATE in ('1','2','3','4') ");
			sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
			sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
			sql.append("GROUP BY T1.RG_STATE");
		
			List<JSONObject> statList = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if(CommonUtil.listIsNotNull(statList)) {
				Map<String, String> statMap = statList.stream().collect(Collectors.toMap(
						(json)->json.getString("RG_STATE"),
						(json)->json.getString("RG_STATE_NUM"),
						(oldVal, newVal)->newVal));
				if(statMap != null) {
					JSONObject result = getJsonResult(statMap);
					if(Constants.openIssue(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId())){
						result.put("openIssue", "1");						
					}
					result.put("total", total);
					return result;
				}
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "根据状态统计质检记录错误" + e.getMessage(), e);
		}
		return getJsonResult(null);
	}
	
	
	/**
	 * 根据质检规则ID查询所有评论
	 * @return
	 */
	@WebControl(name="queryCommentList", type=Types.LIST)
	public JSONObject queryCommentList(){
		EasySQL sql = this.getEasySQL("");
		UserModel user = UserUtil.getUser(request);
		sql.append("SELECT CREATE_ACC, CREATE_NAME,CREATE_TIME,CONTENT FROM "+getTableName("C_CF_COMMON_COMMENTS"));
		sql.append("WHERE 1=1 ");
		sql.append(param.getString("classId"), "AND BUSI_ID=?");
		sql.appendLike(param.getString("CREATE_NAME"), "AND CREATE_NAME LIKE ?");
		sql.append(Constants.COMMENTS_TYPE_QCVOC,"AND TYPE=?");
		sql.append(user.getEpCode(), "AND EP_CODE=?");
		sql.append(user.getBusiOrderId(), "AND BUSI_ORDER_ID=?");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
//	/**
//	 * 任务抽取时 质检组没有数据则调用该接口
//	 * @return
//	 */
//	@WebControl(name="getQCAgentList", type=Types.LIST)
//	public JSONObject getQCAgentList(){
//		EasySQL sql = this.getEasySQL("SELECT" +
//				"	t1.USER_ID," +
//				"	t1.BUSI_ORDER_ID," +
//				"	t1.ROLE_ID," +
//				"	t1.ENT_ID," +
//				"	t1.AGENT_PHONE," +
//				"	t1.AGENT_NAME," +
//				"	t1.PREFIX_NUM," +
//				"	t1.MAX_SERVER," +
//				"	t1.USER_STATE," +
//				"	t1.GROUP_LIST," +
//				"	t1.CREATOR," +
//				"	t1.CREATE_TIME," +
//				"	t1.OUTBOUND," +
//				"	t1.INBOUND," +
//				"	t1.EXT_CONF," +
//				"	t1.ROLE_LIST," +
//				"	t1.ADMIN_FLAG," +
//				"	t4.SKILL_GROUP_NAME," +
//				"	t2.USER_ACCT from ");
//		sql.append(getTableName("CC_BUSI_USER t1 "));
//		sql.append("left join CC_USER t2 on t1.USER_ID = t2.USER_ID ");
//		sql.append("left join " + getTableName("CC_SKILL_GROUP_USER t3 ") + "on t1.USER_ID = t3.USER_ID ");
//		sql.append("left join " + getTableName("CC_SKILL_GROUP t4 ") + "on t3.SKILL_GROUP_ID = t4.SKILL_GROUP_ID ");
//		sql.append(" where 1 = 1 and t2.ADMIN_FLAG = 0  and t2.USER_STATE <> 9 ");
//		sql.append(getBusiOrderId()," and t1.BUSI_ORDER_ID = ? ");
//		sql.append(getEntId()," and t1.ENT_ID = ? ");
//		sql.append(" and t4.SKILL_GROUP_TYPE = 'struct'");
//
////		sql.append(param.getString("roleId"),  " AND EXISTS (SELECT 1 FROM "+getTableName("V_CC_USER_ROLE")+" t3 where t3.USER_ID = t1.USER_ID and t3.ROLE_ID=?) ");
//		sql.appendLike(this.param.getString("userAcct"), "and t2.USER_ACCT like ? ");
//		sql.appendLike(this.param.getString("agentName"), "and t1.AGENT_NAME like ? ");
//		String deptCode = this.param.getString("deptName");
//		if (StringUtils.isNotBlank(deptCode)){
//			String deptCodeList = "";
//			if (!deptCode.contains(",")) {
//				deptCodeList = "'" + deptCode + "'";
//			} else {
//				deptCodeList = convert(deptCode);
//			}
//			sql.append(" and t4.SKILL_GROUP_NAME IN (" + deptCodeList + ")");
//		}
//		sql.appendLike(this.param.getString("skillName"), "and t1.GROUP_LIST like ? ");
//		sql.appendLike(param.getString("roleList"), "and t1.ROLE_LIST like ?");
//		if(ServerContext.isDebug()) {
//			logger.info(CommonUtil.getClassNameAndMethod(this)+ "获取全体质检对象,sql: "+sql.getSQL()+", param: "+JSON.toJSONString(sql.getParams())+"");
//		}
//
//		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
//	}

	/**
	 * 获取任务的坐席质检数量统计  (质检组有数据，调用该接口获取质检对象)
	 * @return
	 */
	@WebControl(name="agentQcNums", type=Types.LIST)
	public JSONObject agentQcNums(){
		String taskId = param.getString("taskId");
		String type = param.getString("numType");
		String dateType = TaskHandler.getDateType(type);
		String currTime = DateUtil.getCurrentDateStr();
		JSONObject resultJson = new JSONObject();
		try {
			EasyQuery query = getQuery();
			List<JSONObject> agentList = new ArrayList<JSONObject>();
			Map<String, String>  task = QueryUtil.getRecordById(getTableName("CC_QC_TASK"), "ID", taskId, query);
			//查找质检组坐席
			if(task != null && task.get("EXAM_GROUP_ID") !=null ) {
				EasySQL findSql = new EasySQL("select AGENT_ID,AGENT_ACC,AGENT_NAME from " + getTableName("CC_QC_GROUP_AGENT"));
				findSql.append("where 1=1");
				findSql.append(task.get("EXAM_GROUP_ID"), "and EXAM_GROUP_ID=?", false);
				findSql.append("01", "and ENABLE_STATUS=?");
				agentList = query.queryForList(findSql.getSQL(), findSql.getParams(), new JSONMapperImpl());
				if(CommonUtil.listIsNull(agentList)) {
					//质检组质检对象为空质检所有人
					EasySQL findSql2 = new EasySQL("select t1.USER_ID AGENT_ID,t1.USER_ACC AGENT_ACC,t1.USER_NAME AGENT_NAME from " + getTableName("CC_USER ") + " t1");
					findSql2.append(" where 1=1");
					findSql2.append(getEntId(), "and t1.ENT_ID=?");
					findSql2.append(getBusiOrderId(), "and t1.BUSI_ORDER_ID=?");
					findSql2.append(taskId, "and USER_ID in (select distinct AGENT_ID from " + getTableName("CC_QC_TASK_OBJ") + " where TASK_ID=? )", false);
					agentList = query.queryForList(findSql2.getSQL(), findSql2.getParams(), new JSONMapperImpl());
				}
			}
			Map<String, Integer> agentNumMap;
			String keyField;
			//查找坐席质检数
			if(task != null && "1".equals(task.get("CHANNEL_TYPE"))) {//语音
				keyField = "AGENT_ID";
				agentNumMap = TaskHandler.getAgnetQcObjNum(taskId, getDbName(), dateType, query);
			}else{//全媒体
				keyField = "AGENT_ACC";
				agentNumMap = TaskHandler.getAgnetQcMediaObjNum(taskId, getDbName(), dateType, query);
			}
			if(CommonUtil.listIsNotNull(agentList) && agentNumMap != null) {
				for(int i=0; i<agentList.size(); i++) {
					JSONObject json = agentList.get(i);
					String key = TaskHandler.getQcNumKey(json.getString(keyField), currTime, dateType);
					json.put("QC_NUM", agentNumMap.get(key));
				}
			}
			resultJson.put("msg", "请求成功!");
			resultJson.put("state", Integer.valueOf(1));
			resultJson.put("data", agentList);
		}catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "获取任务的坐席质检数量统计错误", e);
			resultJson.put("msg", "请求错误!");
			resultJson.put("state", Integer.valueOf(0));
		}
		return resultJson;
	}
	
	
	/**
	 * 质检任务列表   第三方
	 * @return
	 */
	@InfAuthCheck(resId ={"cc-qc-rwgl","cc-qc-rwzx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="objThirdPartyList", type=Types.LIST)
	public JSONObject objThirdPartyList(){
		JSONObject json = new JSONObject();
		try {
			String dateId = param.getString("dateId");
			String startDate = param.getString("beginStartDate");
			String endDate = param.getString("endStartDate");
			String templateId = param.getString("templateId");

			if(StringUtil.isBlank(templateId)) {
				return EasyResult.fail("使用第三方质检模板ID不能为空");
			}		
			if(StringUtil.isBlank(startDate) || StringUtil.isBlank(endDate)) {
				return EasyResult.fail("查找日期不能为空");
			}
			UserModel user = UserUtil.getUser(request);
			//任务数据详情页面，最大查找天数不能大于配置的天数
			int days = DateUtil.bwDays(startDate, endDate, DateUtil.TIME_FORMAT_YMD) + 1;
			int maxDays = Constants.getSearchMaxDays(request);
			if(days > maxDays) {
				return EasyResult.fail("最大查找天数不能超过" + maxDays);
			}
			String taskId = param.getString("taskId");
			String entId = getEntId();
			String busiOrderId = getBusiOrderId();
			EasySQL sql = this.getEasySQL("select T1.CHANNEL_TYPE,T1.ID AS OBJ_ID,T1.SERIAL_ID,T1.RG_STATE AS QC_STATE,T1.RG_RESULT_ID QC_RESULT_ID,T1.CHANNEL_TYPE CALL_TYPE,T1.RG_QC_SCORE RG_SCORE, T1.ZN_QC_SCORE ZN_SCORE, T1.CREATE_TIME,T1.RG_QC_TIME,T1.ONE_VOTE_ITEM,T1.SOURCE");
			sql.append(",T3.ZN_CLASS_ID,T3.EXAM_GROUP_ID,T3.RG_CLASS_ID as CLASS_ID ");
			
			//拿可查询字段
			ThirdPartyFieldCache thirdFieldCache = new ThirdPartyFieldCache();
			
			JSONObject fieldCache = thirdFieldCache.getCache(this.getEntId(), this.getBusiOrderId(), templateId);

			List<JSONObject> itemOptions = new ArrayList<>();
			JSONArray queryForList = fieldCache.getJSONArray("showList");
			for(int i=0;i<queryForList.size();i++){
				JSONObject jsonObject = queryForList.getJSONObject(i);
				

				if("TEMPLATE_ID".equals(jsonObject.getString("FIELD_EN"))){
					continue;
				}		
				sql.append(",T2.");
				sql.append(jsonObject.getString("FIELD_EN"));		
				sql.append(" as THIRD_"+jsonObject.getString("FIELD_EN"));
				
				JSONObject itemOption = new JSONObject();
				itemOption.put("title", jsonObject.getString("FIELD_CN"));
				itemOption.put("key", "THIRD_"+jsonObject.getString("FIELD_EN"));
				
				String type = jsonObject.getString("SHOW_TYPE");
				itemOption.put("showType", type);
				
				JSONObject exJsonObject = JSONObject.parseObject(jsonObject.getString("EX_JSON"));
				
				if("2".equals(type)){				
					itemOption.put("desc", exJsonObject.getString("dictCode"));
				}else if("3".equals(type)){
					String dateFormatter = exJsonObject.getString("dateFormatter");
					if("01".equals(dateFormatter)){
						itemOption.put("format", "yyyy-MM-dd");
					}else if("02".equals(dateFormatter)){
						itemOption.put("format", "yyyy-MM-dd hh:mm:ss");
					}else{
						itemOption.put("format", "yyyy-MM-dd");
					}
				}	
		
				itemOptions.add(itemOption);			
			}
			
			
			sql.append(",T2.TEMPLATE_ID as THIRD_TEMPLATE_ID");
			sql.append(",T2.ID as THIRD_ID");
			
			
			sql.append(" FROM "+getTableName("CC_QC_TASK_OBJ T1"));
			sql.append("LEFT JOIN "+getTableName("cc_qc_third_record T2") +" ON T1.SERIAL_ID = T2.ID ");
			sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
			sql.append("WHERE 1=1 ");
			if(!param.getBooleanValue("isadmin")) {
				//人工抽检状态
				sql.append("AND T1.RG_STATE NOT IN ('0', '1') ");
				//抽检人
				sql.append(user.getUserAcc(), "AND T1.INSPECTOR=?");
			}
			sql.append(param.getString("source")," AND T1.SOURCE = ? ");
			sql.append(taskId,"AND T1.TASK_ID = ?",false);
			sql.append(taskId,"AND T3.ID = ?",false);
			sql.append(entId, " and T1.ENT_ID = ?");
			sql.append(busiOrderId, " and T1.BUSI_ORDER_ID = ?");

			//是否一票否决
			String oneVoteItem = param.getString("oneVoteItem");
			if(StringUtils.isNotBlank(oneVoteItem)){
				if("Y".equals(oneVoteItem)){
					sql.append(" and T1.ONE_VOTE_ITEM >0");
				}else if("N".equals(oneVoteItem)){
					sql.append(" and T1.ONE_VOTE_ITEM =0 ");
				}
			}

			//按统计时间获取数据时
			if(StringUtils.isNotBlank(dateId)){
				sql.append(dateId, " and T2.DATE_ID >= ? ");
				sql.append(dateId, " and T2.DATE_ID <= ?");
			}else{
				if(StringUtils.isNotBlank(startDate)){
					sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
				}
				if(StringUtils.isNotBlank(endDate)){
					sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
				}
			}

			//下一条
			String isNext = this.param.getString("isNext");
			if (StringUtils.equals("Y", isNext)) {
				sql.append(2," and T1.RG_STATE = ?");
			}else{
				sql.append(param.getString("qcStatus")," and T1.RG_STATE = ?");
			}

			
			JSONArray searchForList = fieldCache.getJSONArray("searchList");

			SQLUtil.extendQuery2("T2.",param.getJSONObject("form"),searchForList, sql);
			
			sql.append("ORDER BY T1.RG_RESULT_ID,T1.CREATE_TIME DESC ");
			
			List<JSONObject> selectOptions = new ArrayList<>();
			for(int i=0;i<searchForList.size();i++){
				JSONObject searchObj = searchForList.getJSONObject(i);
				JSONObject jsonObject = new JSONObject();
				jsonObject.put("label", searchObj.getString("FIELD_CN"));
				String showType = searchObj.getString("SHOW_TYPE");
				jsonObject.put("id", searchObj.getString("FIELD_EN"));
				JSONObject exJsonObject = JSONObject.parseObject(searchObj.getString("EX_JSON"));

				if("2".equals(showType)){		
					Map<String, Object> sourceMap = DictCache.getMapAllDictListByGroupCode(this.getEntId(), exJsonObject.getString("dictCode"));		
					List<JSONObject> arrList = new ArrayList<>();

					if(!sourceMap.isEmpty()){
						

						for(Map.Entry<String, Object> entry:sourceMap.entrySet()){  
							//System.out.println(entry.getKey()+"--->"+entry.getValue());  
							//arrObject.put(entry.getKey(), entry.getValue());
							JSONObject arrObject = new JSONObject();

							arrObject.put("key", entry.getKey());
							arrObject.put("value", entry.getValue());
							arrList.add(arrObject);
					    }
					}
					jsonObject.put("arr", arrList);
					
				}else if("3".equals(showType)){
					String dateFormatter = exJsonObject.getString("dateFormatter");
					if("02".equals(dateFormatter)){
						jsonObject.put("format", "yyyy-MM-dd");
					}else{
						jsonObject.put("format", "yyyy-MM-dd hh:mm:ss");
					}
				}
				jsonObject.put("showType", showType);

				selectOptions.add(jsonObject);
		
			}
			
			if(ServerContext.isDebug()) {
				logger.info(CommonUtil.getClassNameAndMethod(this) + "质检任务列表数据 第三方 sql >> " + sql.getSQL() + "， param >> " + JSON.toJSONString(sql.getParams()));
			}
		
			json = queryForPageList(sql.getSQL(), sql.getParams());

			json.put("itemOptions", itemOptions);
			json.put("selectOptions", selectOptions);

			EasySQL sql1 = new EasySQL("select count(*) from " + getTableName("cc_qc_group_inspector") + " T1 ");
			sql1.append("where 1=1");
			sql1.append(getUserId(), "AND T1.USER_ID=?", false);
			sql1.append(taskId, "AND T1.EXAM_GROUP_ID=(select T2.EXAM_GROUP_ID from " +
					getTableName("cc_qc_task") + " T2 WHERE T2.ID=?)", false);
			try {
				boolean isInspector = false;
				isInspector = getQuery().queryForExist(sql1.getSQL(), sql1.getParams());
				json.put("isInspector", isInspector);
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			}
			
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}

		return json;
	}
	
	
	
	/**
	 * 我的质检任务列表
	 * @return
	 */
	/**
	@InfAuthCheck(resId ={"cc-qc-rwgl","cc-qc-rwzx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="objMyList2", type=Types.LIST)
	public JSONObject objMyList2(){
		String dateId = param.getString("dateId");
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		if(StringUtil.isBlank(startDate) || StringUtil.isBlank(endDate)) {
			return EasyResult.fail("查找日期不能为空");
		}
		UserModel user = UserUtil.getUser(request);
		if(param.getBooleanValue("isadmin")) {
			//任务数据详情页面，最大查找天数不能大于配置的天数
			int days = DateUtil.bwDays(startDate, endDate, DateUtil.TIME_FORMAT_YMD) + 1;
			int maxDays = Constants.getMediaSearchMaxDays(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId());
			if(days > maxDays) {
				return EasyResult.fail("最大查找天数不能超过" + maxDays);
			}
		}
		//String taskId = param.getString("taskId");
		String entId = getEntId();
		String busiOrderId = getBusiOrderId();
		EasySQL sql = this.getEasySQL("select T3.ZN_CLASS_ID,T3.EXAM_GROUP_ID,T1.ID AS OBJ_ID,T2.SERIAL_ID,T2.SESSION_ID,T2.DATE_ID,T2.CHANNEL_TYPE,T2.CUST_NAME,T2.CHANNEL_NAME,T2.KEY_NAME,T2.AGENT_PHONE,T2.CASE_STATUS");
		sql.append(",T2.AGENT_NAME,T2.REQ_TIME,T2.BEGIN_TIME,T2.END_TIME,T2.SERVER_TIME,T2.CLEAR_CAUSE,T2.SATISF_CODE,T2.SESSION_SEQ,T1.RG_STATE AS QC_STATE,T3.RG_CLASS_ID as CLASS_ID,T1.RG_RESULT_ID QC_RESULT_ID,T1.CHANNEL_TYPE CALL_TYPE,T1.RG_QC_SCORE RG_SCORE, T1.ZN_QC_SCORE ZN_SCORE, T1.CREATE_TIME,T1.RG_QC_TIME,T1.ONE_VOTE_ITEM,T1.SOURCE ");
		sql.append("FROM "+getTableName("CC_QC_TASK_OBJ T1"));
		sql.append("LEFT JOIN "+getTableName("CC_MEDIA_RECORD T2") +" ON T1.SERIAL_ID = T2.SERIAL_ID ");
		sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
		sql.append("WHERE 1=1 ");
		if(!param.getBooleanValue("isadmin")) {
			//人工抽检状态
			sql.append("AND T1.RG_STATE NOT IN ('0', '1') ");
			//抽检人
			sql.append(user.getUserAcc(), "AND T1.INSPECTOR=?");
		}
		
		sql.append(param.getString("source")," AND T1.SOURCE = ? ");
		sql.append(taskId,"AND T1.TASK_ID = ?",false);
		sql.append(taskId,"AND T3.ID = ?",false);
		sql.append(entId, " and T1.ENT_ID = ?");
		sql.append(busiOrderId, " and T1.BUSI_ORDER_ID = ?");
		sql.append(entId, "AND T2.ENT_ID = ?");// T2.DATE_ID需要配合T2.ENT_ID才能使用联合索引
		sql.append(param.getString("channelType")," AND T2.CHANNEL_TYPE = ? ");
		sql.appendRLike(param.getString("keyName")," AND T2.KEY_NAME like ? ");
        sql.appendLike(param.getString("agentKey")," AND T2.AGENT_NAME like ? ");
        
        //呼入呼出参数  CREATE_CAUSE  IN(1,2,3,4,5,9,10,14) 呼入
        String callType = param.getString("callType");
        if(StringUtils.isNotBlank(callType)){
        	if("01".equals(callType)){
        		sql.append(" and T2.CREATE_CAUSE IN (1,2,3,4,5,9,10,14) "); 
        	}else{
        		sql.append(" and T2.CREATE_CAUSE NOT IN (6,8,29) "); 
        	}
        }
        String oneVoteItem = param.getString("oneVoteItem");
        if(StringUtils.isNotBlank(oneVoteItem)){
        	if("Y".equals(oneVoteItem)){
        		sql.append(" and T1.ONE_VOTE_ITEM >0"); 
        	}else if("N".equals(oneVoteItem)){
        		sql.append(" and T1.ONE_VOTE_ITEM =0 "); 
        	}
        }
        
		//按统计时间获取数据时
		if(StringUtils.isNotBlank(dateId)){
			sql.append(dateId, " and T2.DATE_ID >= ? ");
			sql.append(dateId, " and T2.DATE_ID <= ?");
		}else{
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate.replace("-", ""), " and T2.DATE_ID >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate.replace("-", ""), " and T2.DATE_ID <= ?");
			}
		}
		
		sql.append(param.getString("satisf")," and T2.SATISF_CODE = ?");
		sql.append(param.getString("sessionSeq")," and T2.SESSION_SEQ = ?");
		sql.append(param.getString("qcStatus")," and T1.RG_STATE = ?");
		
		sql.append("ORDER BY T1.RG_RESULT_ID,T1.CREATE_TIME DESC ");
		
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + "质检任务列表数据 全媒体 sql >> " + sql.getSQL() + "， param >> " + JSON.toJSONString(sql.getParams()));
		}
		
		JSONObject json = queryForPageList(sql.getSQL(), sql.getParams());
		
		EasySQL sql1 = new EasySQL("select count(*) from " + getTableName("cc_qc_group_inspector") + " T1 ");
		sql1.append("where 1=1");
		sql1.append(getUserId(), "AND T1.USER_ID=?", false);
		sql1.append(taskId, "AND T1.EXAM_GROUP_ID=(select T2.EXAM_GROUP_ID from " +
				getTableName("cc_qc_task") + " T2 WHERE T2.ID=?)", false);
		try {
			boolean isInspector = false;
			isInspector = getQuery().queryForExist(sql1.getSQL(), sql1.getParams());
			json.put("isInspector", isInspector);
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return json;
	}
	*/
	
	/**
	 * 
	 * @return
	 */
	@InfAuthCheck(resId ={"cc-qc-rwgl","cc-qc-rwzx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="objMyList", type=Types.LIST)
	public JSONObject objMyList(){
		EasySQL sql = this.getEasySQL("");
		UserModel user = UserUtil.getUser(request);
		String openCoachScore =  SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OPEN_COACH_SCORE");
		String startDate = param.getString("beginStartDate");
		String endDate =param.getString("endStartDate");
		String qcStatus =param.getString("qcStatus");
		
		String startQcTime = param.getString("beginQcTime");
		String endQcTime = param.getString("endQcTime");
		boolean startDateBlank  = StringUtil.isBlank(startDate) || StringUtil.isBlank(endDate);
		boolean qcTimeBlank  = StringUtil.isBlank(startQcTime) || StringUtil.isBlank(endQcTime);
		if(startDateBlank && qcTimeBlank) {
			return EasyResult.fail("查找日期不能为空");
		}
		String agentKey = param.getString("agentKey");
		String caller = param.getString("caller");
		String serialId = param.getString("serialId");
		String channelType = param.getString("channelType");
		if( StringUtil.isBlank(channelType) || StringUtil.equals("1", channelType)) {
			
			if(StringUtil.isNotBlank(sql.getSQL())){
				sql.append(" union all ");
			}
			
			//语音
			sql.append("select T3.RG_CLASS_ID CLASS_ID,T3.TEMPLATE_ID,T1.ID OBJ_ID,T1.CHANNEL_TYPE,T1.CHANNEL_TYPE CALL_TYPE,'语音' CHANNEL,T1.RG_RESULT_ID QC_RESULT_ID,T3.TASK_NAME,T3.EXAM_GROUP_ID,T3.ZN_CLASS_ID,T1.INSPECTOR,T1.INSPECTOR_NAME,T2.CASE_STATUS,T2.SERIAL_ID,T2.DATE_ID,T2.CUST_PHONE as CUST_NAME"
					+ ",T2.BILL_BEGIN_TIME AS BEGIN_TIME,T2.BILL_END_TIME AS END_TIME,T1.AGENT_NAME,'2' AS TYPE,T1.INSPECTOR_TIME,T1.RG_RESULT_ID,T1.CREATE_TIME, T2.BILL_TIME SERVER_TIME,T1.AGENT_ACC,T1.RG_QC_TIME,T1.TASK_ID,T1.ZN_QC_SCORE,T1.RG_QC_SCORE,T4.EVALUATE,T4.EVALUATE2,T1.RG_STATE QC_STATE,T1.ONE_VOTE_ITEM,T1.SOURCE,T2.CALLER,T2.SERIAL_ID AS CHANNEL_SERIAL_ID ");
			sql.append("FROM "+getTableName("CC_QC_TASK_OBJ T1"));
			sql.append("LEFT JOIN "+getTableName("CC_CALL_RECORD T2") +" ON T1.SERIAL_ID = T2.SERIAL_ID ");
			sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
			sql.append("LEFT JOIN "+getTableName("CC_QC_RESULT")+" T4 ON T4.QC_RESULT_ID = T1.RG_RESULT_ID");
			sql.append("WHERE 1=1 ");
			if(StringUtils.isBlank(qcStatus)){
				sql.append(" and T1.RG_STATE in ('2','3','4') ");
			}else{
				if ("2".equals(qcStatus) || "4".equals(qcStatus)){
					sql.append(qcStatus," and T1.RG_STATE=? ");
				} else if ("5".equals(qcStatus)) {
					sql.append("and T1.RG_STATE = '4' and T4.APPROVAL_STATE IN ('1','2')");
				} else if ("6".equals(qcStatus)) {
					sql.append("and T1.RG_STATE = '4' and T4.CACH_STATE = '1' and T4.APPROVAL_STATE = '5'");
					if(StringUtil.equals("Y", openCoachScore)) {
						sql.append(Constants.getPassCoachScore()," and t1.RG_QC_SCORE <= ?");
					}
				}
			}
			
			sql.append(user.getEpCode(), " and T1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and T1.BUSI_ORDER_ID = ?");
			sql.append("1"," AND T1.CHANNEL_TYPE= ? ");
			sql.append(param.getString("taskId")," AND T1.TASK_ID= ? ");
			//服务开始时间
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate + " 00:00:00", " and T1.SERVICE_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate + " 23:59:59", " and T1.SERVICE_TIME <=  ?");
			}
			
			if(StringUtils.isNotBlank(startQcTime)){
				sql.append(startQcTime, " and T1.RG_QC_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endQcTime)){
				sql.append(endQcTime + " 23:59:59", " and T1.RG_QC_TIME <= ?");
			}
//			sql.appendLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
			
			sql.append(user.getEpCode(), " and T2.ENT_ID = ?");
			if(StringUtils.isNotBlank(startDate)){
				sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
			}
			sql.appendRLike(agentKey," AND T1.AGENT_NAME like ? ");
			sql.appendRLike(caller," AND T2.CALLER like ? ");
			sql.appendRLike(serialId," AND T2.SERIAL_ID like ? ");
			sql.append(user.getUserAcc(), "AND T1.INSPECTOR = ?");
			
			//sql.append("ORDER BY T1.RG_RESULT_ID,T1.CREATE_TIME DESC");
		}
		if(StringUtil.isBlank(channelType) ||StringUtil.equals("2", channelType)){
			//全媒体
			
			if(StringUtil.isNotBlank(sql.getSQL())){
				sql.append(" union all ");
			}
			
			
			sql.append("SELECT T3.RG_CLASS_ID CLASS_ID,T3.TEMPLATE_ID,T1.ID OBJ_ID,T1.CHANNEL_TYPE,T1.CHANNEL_TYPE CALL_TYPE,T2.CHANNEL_NAME CHANNEL,T1.RG_RESULT_ID QC_RESULT_ID,T3.TASK_NAME,T3.EXAM_GROUP_ID,T3.ZN_CLASS_ID, T1.INSPECTOR,T1.INSPECTOR_NAME,T2.CASE_STATUS,T2.SERIAL_ID,T2.DATE_ID"
					+ ",T2.CUST_NAME,T2.BEGIN_TIME,T2.END_TIME,T1.AGENT_NAME,'1' AS TYPE,T1.INSPECTOR_TIME,T1.RG_RESULT_ID,T1.CREATE_TIME,T2.SERVER_TIME,T1.AGENT_ACC,T1.RG_QC_TIME,T1.TASK_ID,T1.ZN_QC_SCORE,T1.RG_QC_SCORE,T4.EVALUATE,T4.EVALUATE2,T1.RG_STATE QC_STATE,T1.ONE_VOTE_ITEM,T1.SOURCE,T2.CUST_CODE AS CALLER,T2.SERIAL_ID AS CHANNEL_SERIAL_ID  FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
			sql.append("LEFT JOIN  " + getTableName("CC_MEDIA_RECORD") + " T2 ON T1.SERIAL_ID = T2.SERIAL_ID ");
			sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
			sql.append("LEFT JOIN "+getTableName("CC_QC_RESULT")+" T4 ON T4.QC_RESULT_ID = T1.RG_RESULT_ID");
			sql.append("WHERE 1=1 ");
			if(StringUtils.isBlank(qcStatus)){
				sql.append(" and T1.RG_STATE in ('2','3','4') ");
			}else{
				if ("2".equals(qcStatus) || "4".equals(qcStatus)){
					sql.append(qcStatus," and T1.RG_STATE=? ");
				} else if ("5".equals(qcStatus)) {
					sql.append("and T1.RG_STATE = '4' and T4.APPROVAL_STATE IN ('1','2')");
				} else if ("6".equals(qcStatus)) {
					sql.append("and T1.RG_STATE = '4' and T4.CACH_STATE = '1' and T4.APPROVAL_STATE = '5'");
					if(StringUtil.equals("Y", openCoachScore)) {
						sql.append(Constants.getPassCoachScore()," and t1.RG_QC_SCORE <= ?");
					}
				}
			}
			sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
			sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
			sql.append("2"," AND T1.CHANNEL_TYPE= ? ");
			sql.append(param.getString("taskId")," AND T1.TASK_ID= ? ");
			//服务开始时间
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate + " 00:00:00", " and T1.SERVICE_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate + " 23:59:59", " and T1.SERVICE_TIME <=  ?");
			}
			sql.append(user.getEpCode(), " and T2.ENT_ID = ?");
			
			if(StringUtils.isNotBlank(startDate)){
				sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
			}
			
			if(StringUtils.isNotBlank(startQcTime)){
				sql.append(startQcTime, " and T1.RG_QC_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endQcTime)){
				sql.append(endQcTime + " 23:59:59", " and T1.RG_QC_TIME <= ?");
			}
//			sql.appendRLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
			sql.appendRLike(agentKey," AND T1.AGENT_NAME like ? ");
			sql.appendRLike(caller," AND T2.CUST_CODE like ? ");
			sql.appendRLike(serialId," AND T2.SERIAL_ID like ? ");
			sql.append(user.getUserAcc(), "AND T1.INSPECTOR = ?");

			//sql.append("ORDER BY T1.RG_RESULT_ID,T1.CREATE_TIME DESC");
		}
		
		if(StringUtil.isBlank(channelType) || StringUtil.equals("3", channelType)){
			//邮件
			
			if(StringUtil.isNotBlank(sql.getSQL())){
				sql.append(" union all ");
			}
			
			
			sql.append("SELECT T3.RG_CLASS_ID CLASS_ID,T3.TEMPLATE_ID,T1.ID OBJ_ID,T1.CHANNEL_TYPE,T1.CHANNEL_TYPE CALL_TYPE,'邮件' CHANNEL,T1.RG_RESULT_ID QC_RESULT_ID,T3.TASK_NAME,T3.EXAM_GROUP_ID,T3.ZN_CLASS_ID, T1.INSPECTOR,T1.INSPECTOR_NAME,'' CASE_STATUS,T2.ID SERIAL_ID,T2.DATE_ID"
					+ ",T2.EMAIL_FROM_NAME CUST_NAME,T2.CREATE_TIME BEGIN_TIME,T2.CLOSE_TIME END_TIME,T1.AGENT_NAME,'1' AS TYPE,T1.INSPECTOR_TIME,T1.RG_RESULT_ID,T1.CREATE_TIME,1 as SERVER_TIME,T1.AGENT_ACC,T1.RG_QC_TIME,T1.TASK_ID,T1.ZN_QC_SCORE,T1.RG_QC_SCORE,T4.EVALUATE,T4.EVALUATE2,T1.RG_STATE QC_STATE,T1.ONE_VOTE_ITEM,T1.SOURCE,T2.EMAIL_FROM AS CALLER,T2.ID AS CHANNEL_SERIAL_ID  FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
			sql.append("LEFT JOIN  " + getTableName("c_email_session") + " T2 ON T1.SERIAL_ID = T2.ID ");
			sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
			sql.append("LEFT JOIN "+getTableName("CC_QC_RESULT")+" T4 ON T4.QC_RESULT_ID = T1.RG_RESULT_ID");
			sql.append("WHERE 1=1 ");
			if(StringUtils.isBlank(qcStatus)){
				sql.append(" and T1.RG_STATE in ('2','3','4') ");
			}else{
				if ("2".equals(qcStatus) || "4".equals(qcStatus)){
					sql.append(qcStatus," and T1.RG_STATE=? ");
				} else if ("5".equals(qcStatus)) {
					sql.append("and T1.RG_STATE = '4' and T4.APPROVAL_STATE IN ('1','2')");
				} else if ("6".equals(qcStatus)) {
					sql.append("and T1.RG_STATE = '4' and T4.CACH_STATE = '1' and T4.APPROVAL_STATE = '5'");
					if(StringUtil.equals("Y", openCoachScore)) {
						sql.append(Constants.getPassCoachScore()," and t1.RG_QC_SCORE <= ?");
					}
				}
			}
			
			sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
			sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
			sql.append("3"," AND T1.CHANNEL_TYPE= ? ");
			sql.append(param.getString("taskId")," AND T1.TASK_ID= ? ");
			//服务开始时间
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate + " 00:00:00", " and T1.SERVICE_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate + " 23:59:59", " and T1.SERVICE_TIME <=  ?");
			}
			sql.append(user.getEpCode(), " and T2.ENT_ID = ?");
			if(StringUtils.isNotBlank(startDate)){
				sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
			}
			if(StringUtils.isNotBlank(startQcTime)){
				sql.append(startQcTime, " and T1.RG_QC_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endQcTime)){
				sql.append(endQcTime + " 23:59:59", " and T1.RG_QC_TIME <= ?");
			}
//			sql.appendRLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
			sql.appendRLike(agentKey," AND T1.AGENT_NAME like ? ");
			sql.appendRLike(caller," AND T2.EMAIL_FROM like ? ");
			sql.appendRLike(serialId," AND T2.ID like ? ");
			sql.append(user.getUserAcc(), "AND T1.INSPECTOR = ?");

			//sql.append("ORDER BY T1.RG_RESULT_ID,T1.CREATE_TIME DESC");
		}
		
		if(StringUtil.isBlank(channelType) || StringUtil.equals("9", channelType)){
			if(StringUtil.isNotBlank(sql.getSQL())){
				sql.append(" union all ");
			}
			
			
			//第三方
			sql.append("SELECT T3.RG_CLASS_ID CLASS_ID,T3.TEMPLATE_ID,T1.ID OBJ_ID,T1.CHANNEL_TYPE,T1.CHANNEL_TYPE CALL_TYPE,'自定义' as CHANNEL,T1.RG_RESULT_ID QC_RESULT_ID,T3.TASK_NAME,T3.EXAM_GROUP_ID,T3.ZN_CLASS_ID, T1.INSPECTOR,T1.INSPECTOR_NAME,'' CASE_STATUS,T2.SERIAL_ID,T2.DATE_ID"
					+ ",T2.CALLER CUST_NAME,T2.BEGIN_TIME,T2.END_TIME,T1.AGENT_NAME,'1' AS TYPE,T1.INSPECTOR_TIME,T1.RG_RESULT_ID,T1.CREATE_TIME, 1 as SERVER_TIME,T1.AGENT_ACC,T1.RG_QC_TIME,T1.TASK_ID,T1.ZN_QC_SCORE,T1.RG_QC_SCORE,T4.EVALUATE,T4.EVALUATE2,T1.RG_STATE QC_STATE,T1.ONE_VOTE_ITEM,T1.SOURCE,T2.CALLER,T2.OBJECT_ID AS CHANNEL_SERIAL_ID  FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
			sql.append("LEFT JOIN  " + getTableName("cc_qc_third_record") + " T2 ON T1.SERIAL_ID = T2.ID ");//T2.BEGIN_TIME
			sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
			sql.append("LEFT JOIN "+getTableName("CC_QC_RESULT")+" T4 ON T4.QC_RESULT_ID = T1.RG_RESULT_ID");
			sql.append("WHERE 1=1 ");
			if(StringUtils.isBlank(qcStatus)){
				sql.append(" and T1.RG_STATE in ('2','3','4') ");
			}else{
				if ("2".equals(qcStatus) || "4".equals(qcStatus)){
					sql.append(qcStatus," and T1.RG_STATE=? ");
				} else if ("5".equals(qcStatus)) {
					sql.append("and T1.RG_STATE = '4' and T4.APPROVAL_STATE IN ('1','2')");
				} else if ("6".equals(qcStatus)) {
					sql.append("and T1.RG_STATE = '4' and T4.CACH_STATE = '1' and T4.APPROVAL_STATE = '5'");
					if(StringUtil.equals("Y", openCoachScore)) {
						sql.append(Constants.getPassCoachScore()," and t1.RG_QC_SCORE <= ?");
					}
				}
			}
			
			sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
			sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
			sql.append("9"," AND T1.CHANNEL_TYPE= ? ");
			sql.append(param.getString("taskId")," AND T1.TASK_ID= ? ");
			//服务开始时间
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate + " 00:00:00", " and T1.SERVICE_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate + " 23:59:59", " and T1.SERVICE_TIME <=  ?");
			}
			sql.append(user.getEpCode(), " and T2.ENT_ID = ?");
			if(StringUtils.isNotBlank(startDate)){
				sql.append(CommonUtil.parseInteger(startDate.replace("-", "")), " and T2.DATE_ID >= ? ");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replace("-", "")), " and T2.DATE_ID <= ?");
			}
			if(StringUtils.isNotBlank(startQcTime)){
				sql.append(startQcTime, " and T1.RG_QC_TIME >= ? ");
			}
			if(StringUtils.isNotBlank(endQcTime)){
				sql.append(endQcTime + " 23:59:59", " and T1.RG_QC_TIME <= ?");
			}
//			sql.appendRLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
			sql.appendRLike(agentKey," AND T1.AGENT_NAME like ? ");
			sql.appendRLike(caller," AND T2.CALLER like ? ");
			sql.appendRLike(serialId," AND T2.OBJECT_ID like ? ");
			sql.append(user.getUserAcc(), "AND T1.INSPECTOR = ?");

			//sql.append("ORDER BY T1.RG_RESULT_ID,T1.CREATE_TIME DESC");
		}
		
		if(StringUtil.isBlank(channelType) || StringUtil.equals("4", channelType)){
			String openOrderQcBtn = Constants.getOpenOrderQcBtn(user.getSchemaName(),user.getEpCode(),user.getBusiOrderId());
			logger.info("是否开启工单质检:"+openOrderQcBtn);
			if (StringUtils.equals("Y",openOrderQcBtn)){
				//工单
				if(StringUtil.isNotBlank(sql.getSQL())){
					sql.append(" union all ");
				}
				
				
				sql.append("SELECT T3.RG_CLASS_ID CLASS_ID,T3.TEMPLATE_ID,T1.ID OBJ_ID,T1.CHANNEL_TYPE,T1.CHANNEL_TYPE CALL_TYPE,'工单' CHANNEL,T1.RG_RESULT_ID QC_RESULT_ID,T3.TASK_NAME,T3.EXAM_GROUP_ID,T3.ZN_CLASS_ID, T1.INSPECTOR,T1.INSPECTOR_NAME,'' CASE_STATUS,T2.ID SERIAL_ID,'' as DATE_ID"
						+ ",'' CUST_NAME,T2.CREATE_TIME BEGIN_TIME,T2.END_TIME ,T1.AGENT_NAME,'4' AS TYPE,T1.INSPECTOR_TIME,T1.RG_RESULT_ID,T1.CREATE_TIME,1 as SERVER_TIME,T1.AGENT_ACC,T1.RG_QC_TIME,T1.TASK_ID,T1.ZN_QC_SCORE,T1.RG_QC_SCORE,T4.EVALUATE,T4.EVALUATE2,T1.RG_STATE QC_STATE,T1.ONE_VOTE_ITEM,T1.SOURCE,T2.CALLER,T2.ORDER_NO AS CHANNEL_SERIAL_ID  FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
				sql.append("LEFT JOIN  " + getTableName("C_BO_BASE_ORDER") + " T2 ON T1.SERIAL_ID = T2.ID ");
				sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
				sql.append("LEFT JOIN "+getTableName("CC_QC_RESULT")+" T4 ON T4.QC_RESULT_ID = T1.RG_RESULT_ID");
				sql.append("WHERE 1=1 ");
				if(StringUtils.isBlank(qcStatus)){
					sql.append(" and T1.RG_STATE in ('2','3','4') ");
				}else{
					if ("2".equals(qcStatus) || "4".equals(qcStatus)){
						sql.append(qcStatus," and T1.RG_STATE=? ");
					} else if ("5".equals(qcStatus)) {
						sql.append("and T1.RG_STATE = '4' and T4.APPROVAL_STATE IN ('1','2')");
					} else if ("6".equals(qcStatus)) {
						sql.append("and T1.RG_STATE = '4' and T4.CACH_STATE = '1' and T4.APPROVAL_STATE = '5'");
						if(StringUtil.equals("Y", openCoachScore)) {
							sql.append(Constants.getPassCoachScore()," and t1.RG_QC_SCORE <= ?");
						}
					}
				}
				
				sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
				sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
				sql.append("4"," AND T1.CHANNEL_TYPE= ? ");
				sql.append(param.getString("taskId")," AND T1.TASK_ID= ? ");
				//服务开始时间
				if(StringUtils.isNotBlank(startDate)){
					sql.append(startDate + " 00:00:00", " and T1.SERVICE_TIME >= ? ");
				}
				if(StringUtils.isNotBlank(endDate)){
					sql.append(endDate + " 23:59:59", " and T1.SERVICE_TIME <=  ?");
				}
				if(StringUtils.isNotBlank(startQcTime)){
					sql.append(startQcTime, " and T1.RG_QC_TIME >= ? ");
				}
				if(StringUtils.isNotBlank(endQcTime)){
					sql.append(endQcTime + " 23:59:59", " and T1.RG_QC_TIME <= ?");
				}
//				sql.appendRLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
				sql.appendRLike(agentKey," AND T1.AGENT_NAME like ? ");
				sql.appendRLike(caller," AND T2.CALLER like ? ");
				sql.appendRLike(serialId," AND T2.ORDER_NO like ? ");
				sql.append(user.getUserAcc(), "AND T1.INSPECTOR = ?");
				//防止重复出现抽取出的工单数据
				sql.append(user.getEpCode(), "AND T2.EP_CODE=?");
				
				sql.append(" union all ");
				
				
				sql.append("SELECT T3.RG_CLASS_ID CLASS_ID,T3.TEMPLATE_ID,T1.ID OBJ_ID,T1.CHANNEL_TYPE,T1.CHANNEL_TYPE CALL_TYPE,'工单' CHANNEL,T1.RG_RESULT_ID QC_RESULT_ID,T3.TASK_NAME,T3.EXAM_GROUP_ID,T3.ZN_CLASS_ID, T1.INSPECTOR,T1.INSPECTOR_NAME,'' CASE_STATUS,T2.ID SERIAL_ID,'' as DATE_ID"
						+ ",'' CUST_NAME,T2.CREATE_TIME BEGIN_TIME,T2.END_TIME ,T1.AGENT_NAME,'4' AS TYPE,T1.INSPECTOR_TIME,T1.RG_RESULT_ID,T1.CREATE_TIME,1 as SERVER_TIME,T1.AGENT_ACC,T1.RG_QC_TIME,T1.TASK_ID,T1.ZN_QC_SCORE,T1.RG_QC_SCORE,T4.EVALUATE,T4.EVALUATE2,T1.RG_STATE QC_STATE,T1.ONE_VOTE_ITEM,T1.SOURCE,T2.CALLER,T2.ORDER_NO AS CHANNEL_SERIAL_ID  FROM "+getTableName("CC_QC_TASK_OBJ")+" T1 ");
				sql.append("LEFT JOIN  " + getTableName("C_BO_BASE_ORDER_HIS") + " T2 ON T1.SERIAL_ID = T2.ID ");
				sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID");
				sql.append("LEFT JOIN "+getTableName("CC_QC_RESULT")+" T4 ON T4.QC_RESULT_ID = T1.RG_RESULT_ID");
				sql.append("WHERE 1=1 ");
				if(StringUtils.isBlank(qcStatus)){
					sql.append(" and T1.RG_STATE in ('2','3','4') ");
				}else{
					if ("2".equals(qcStatus) || "4".equals(qcStatus)){
						sql.append(qcStatus," and T1.RG_STATE=? ");
					} else if ("5".equals(qcStatus)) {
						sql.append("and T1.RG_STATE = '4' and T4.APPROVAL_STATE IN ('1','2')");
					} else if ("6".equals(qcStatus)) {
						sql.append("and T1.RG_STATE = '4' and T4.CACH_STATE = '1' and T4.APPROVAL_STATE = '5'");
						if(StringUtil.equals("Y", openCoachScore)) {
							sql.append(Constants.getPassCoachScore()," and t1.RG_QC_SCORE <= ?");
						}
					}
				}
				sql.append(user.getEpCode(), "AND T1.ENT_ID=?");
				sql.append(user.getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
				sql.append("4"," AND T1.CHANNEL_TYPE= ? ");
				sql.append(param.getString("taskId")," AND T1.TASK_ID= ? ");
				//服务开始时间
				if(StringUtils.isNotBlank(startDate)){
					sql.append(startDate + " 00:00:00", " and T1.SERVICE_TIME >= ? ");
				}
				if(StringUtils.isNotBlank(endDate)){
					sql.append(endDate + " 23:59:59", " and T1.SERVICE_TIME <=  ?");
				}
				if(StringUtils.isNotBlank(startQcTime)){
					sql.append(startQcTime, " and T1.RG_QC_TIME >= ? ");
				}
				if(StringUtils.isNotBlank(endQcTime)){
					sql.append(endQcTime + " 23:59:59", " and T1.RG_QC_TIME <= ?");
				}
//				sql.appendRLike(param.getString("inspectorAcc"), "AND T1.INSPECTOR like ?");
				sql.appendRLike(agentKey," AND T1.AGENT_NAME like ? ");
				sql.appendRLike(caller," AND T2.CALLER like ? ");
				sql.appendRLike(serialId," AND T2.ORDER_NO like ? ");
				sql.append(user.getUserAcc(), "AND T1.INSPECTOR = ?");
				sql.append(user.getEpCode(), "AND T2.EP_CODE=?");

			}
			
		}

		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + "查找质检数据 sql >> " + sql.getSQL() + ", param >> " + JSON.toJSONString(sql.getParams()));
		}
		PhoneCryptor cryptor = PhoneCryptor.getInstance();
		String entId = this.getEntId();
		
		JSONObject list = queryForPageList(sql.getSQL(), sql.getParams());
		if (StringUtil.equals("1", channelType) && list != null) {
			list.put("data", cryptor.decrypt(list.getJSONArray("data"), new String[]{"CUST_NAME"}, false));
		}else if (StringUtil.isBlank(channelType) && list != null) {
			JSONArray data = list.getJSONArray("data");
			if (data!=null) {
				for (int i = 0; i < data.size(); i++) {
					JSONObject row = data.getJSONObject(i);
					String rowChannelType = row.getString("CHANNEL_TYPE");
					if (StringUtils.equals("1", rowChannelType)) {
						row.put("CUST_NAME", cryptor.decrypt(entId, row.getString("CUST_NAME")));
					}
				}
				list.replace("data", data);
			}
			
		}
		
		return list;
	}
	
	/**
	 * 工单质检列表
	 * @return
	 */
	@InfAuthCheck(resId ={"cc-qc-rwgl","cc-qc-rwzx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="objOrderList", type=Types.LIST)
	public JSONObject objOrderList(){
		UserModel user = UserUtil.getUser(request);
//		String dateId = param.getString("dateId");
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		if(StringUtil.isBlank(startDate) || StringUtil.isBlank(endDate)) {
			return EasyResult.fail("查找日期不能为空");
		}
		int days = DateUtil.bwDays(startDate, endDate, DateUtil.TIME_FORMAT_YMD) + 1;
		int maxDays = Constants.getSearchMaxDays(request);
		if(days > maxDays) {
			return EasyResult.fail("最大查找天数不能超过" + maxDays);
		}
		String taskId = param.getString("taskId");
		String entId = getEntId();
		String busiOrderId = getBusiOrderId();
		EasySQL sql = this.getEasySQL("SELECT * FROM (");
		
		sql.append("SELECT T1.SERIAL_ID,T1.CHANNEL_TYPE,T1.RG_RESULT,T1.INSPECTOR_NAME,T1.RG_STATE AS QC_STATE,T3.ZN_CLASS_ID,T3.EXAM_GROUP_ID,T1.ID AS OBJ_ID,T1.IS_TYPEICAL,T1.LABEL_CONTENT");
		sql.append(",t3.RG_CLASS_ID as CLASS_ID,T1.RG_RESULT_ID QC_RESULT_ID,T1.CHANNEL_TYPE CALL_TYPE, T1.RG_QC_SCORE RG_SCORE, T1.ZN_QC_SCORE ZN_SCORE, T1.RG_QC_TIME,T1.ONE_VOTE_ITEM,T1.SOURCE ");
		sql.append(",t2.ORDER_NO,t2.CREATE_ACC,t2.CREATE_NAME,t2.CREATE_TIME,t2.END_TIME");
		sql.append(",t2.ORDER_LEVEL,t2.PROVINCE,t2.CITY,t2.AREA,t2.SOURCE_TYPE ,t2.CUIBAN_NUM,t2.ORDER_CHARGE,t4.USERNAME ORDER_CHARGE_NAME");
		
		sql.append("FROM "+getTableName("CC_QC_TASK_OBJ T1"));
		sql.append("LEFT JOIN "+getTableName("C_BO_BASE_ORDER t2") +" ON T1.SERIAL_ID = T2.ID ");
		sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID ");
		sql.append("LEFT JOIN CC_USER T4 ON T4.USER_ACCT = T2.ORDER_CHARGE ");

		sql.append("WHERE 1=1 ");
		if(!param.getBooleanValue("isadmin")) {
			sql.append("AND T1.RG_STATE NOT IN ('0', '1') ");
			sql.append(user.getUserAcc(), "AND T1.INSPECTOR=?");
		}
		sql.append(param.getString("source")," AND T1.SOURCE = ? ");
		sql.append(taskId,"AND T3.ID = ?",false);
		sql.append(entId, " and T1.ENT_ID = ?");
		sql.append(busiOrderId, " and T1.BUSI_ORDER_ID = ?");
		sql.append(taskId,"AND T1.TASK_ID = ?",false);
		
        sql.appendLike(param.getString("agentKey"), "and T4.USERNAME like ? ");
        
    	sql.append(entId, "AND T2.EP_CODE = ?"); 
		if(StringUtils.isNotBlank(startDate)){
			sql.append(startDate+" 00:00:00", " and T2.END_TIME >= ? ");
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(endDate+" 23:59:59", " and T2.END_TIME <= ?");
		}
		//下一条
		String isNext = this.param.getString("isNext");
		if (StringUtils.equals("Y", isNext)) {
			sql.append(2," and T1.RG_STATE = ?");
		}else{
			sql.append(param.getString("qcStatus")," and T1.RG_STATE = ?");
		}
		
		
		//兼容老远平的工单历史表
		sql.append("union all");
		
		sql.append("SELECT T1.SERIAL_ID,T1.CHANNEL_TYPE,T1.RG_RESULT,T1.INSPECTOR_NAME,T1.RG_STATE AS QC_STATE,T3.ZN_CLASS_ID,T3.EXAM_GROUP_ID,T1.ID AS OBJ_ID,T1.IS_TYPEICAL,T1.LABEL_CONTENT");
		sql.append(",t3.RG_CLASS_ID as CLASS_ID,T1.RG_RESULT_ID QC_RESULT_ID,T1.CHANNEL_TYPE CALL_TYPE, T1.RG_QC_SCORE RG_SCORE, T1.ZN_QC_SCORE ZN_SCORE, T1.RG_QC_TIME,T1.ONE_VOTE_ITEM,T1.SOURCE ");
		sql.append(",t2.ORDER_NO,t2.CREATE_ACC,t2.CREATE_NAME,t2.CREATE_TIME,t2.END_TIME");
		sql.append(",t2.ORDER_LEVEL,t2.PROVINCE,t2.CITY,t2.AREA,t2.SOURCE_TYPE,t2.CUIBAN_NUM,t2.ORDER_CHARGE,t4.USERNAME ORDER_CHARGE_NAME");
		
		sql.append("FROM "+getTableName("CC_QC_TASK_OBJ T1"));
		sql.append("LEFT JOIN "+getTableName("C_BO_BASE_ORDER_HIS t2") +" ON T1.SERIAL_ID = T2.ID ");
		sql.append("LEFT JOIN "+getTableName("CC_QC_TASK")+" T3 ON T1.TASK_ID = T3.ID ");
		sql.append("LEFT JOIN CC_USER T4 ON T4.USER_ACCT = T2.ORDER_CHARGE ");

		sql.append("WHERE 1=1 ");
		if(!param.getBooleanValue("isadmin")) {
			sql.append("AND T1.RG_STATE NOT IN ('0', '1') ");
			sql.append(user.getUserAcc(), "AND T1.INSPECTOR=?");
		}
		sql.append(param.getString("source")," AND T1.SOURCE = ? ");
		sql.append(taskId,"AND T3.ID = ?",false);
		sql.append(entId, " and T1.ENT_ID = ?");
		sql.append(busiOrderId, " and T1.BUSI_ORDER_ID = ?");
		sql.append(taskId,"AND T1.TASK_ID = ?",false);
		
		sql.appendLike(param.getString("agentKey"), "and T4.USERNAME like ? ");
        
    	sql.append(entId, "AND T2.EP_CODE = ?"); 
    	if(StringUtils.isNotBlank(startDate)){
			sql.append(startDate+" 00:00:00", " and T2.END_TIME >= ? ");
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(endDate+" 23:59:59", " and T2.END_TIME <= ?");
		}
		//下一条
		if (StringUtils.equals("Y", isNext)) {
			sql.append(2," and T1.RG_STATE = ?");
		}else{
			sql.append(param.getString("qcStatus")," and T1.RG_STATE = ?");
		}
		
		sql.append(" ) t ORDER BY  t.CREATE_TIME DESC ");
		if(ServerContext.isDebug()){
			logger.info(CommonUtil.getClassNameAndMethod(this) + "查询sql >> " + sql.getSQL() + ", param >> " + JSON.toJSONString(sql.getParams()));;
		}
		JSONObject json = queryForPageList(sql.getSQL(), sql.getParams());
		
		EasySQL sql1 = new EasySQL("select count(*) from " + getTableName("cc_qc_group_inspector") + " T1 ");
		sql1.append("where 1=1");
		sql1.append(getUserId(), "AND T1.USER_ID=?", false);
		sql1.append(taskId, "AND T1.EXAM_GROUP_ID=(select T2.EXAM_GROUP_ID from " +
				getTableName("cc_qc_task") + " T2 WHERE T2.ID=?)", false);
		try {
			boolean isInspector = false;
			isInspector = getQuery().queryForExist(sql1.getSQL(), sql1.getParams());
			json.put("isInspector", isInspector);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计质检数据错误", e);
		}
		return json;
	}

	/**
	 * 获取质检清单查询接口
	 */
	@InfAuthCheck(resId ={"cc-qc-zjqd","cc-qc-rwzx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="getAllQualityList", type=Types.LIST)
	public JSONObject getAllQualityList() {
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		String qcOrder = param.getString("qcOrder"); //1：智能+人工 2：仅人工 3：仅智能
		String channelType = param.getString("channelType");
		String agentAcc = param.getString("agentAcc");
		String agentName = param.getString("agentName");
		String inspector = param.getString("inspector");
		String inspectorName = param.getString("inspectorName");
		String objState = param.getString("objState");
		String caller = param.getString("caller");
		String satisfyId = param.getString("satisfyId");
		String satisfyName = param.getString("satisfyName");
		String cachState = param.getString("cachState");
		String rgResult = param.getString("rgResult"); // 1:合格  2:致命差错  3:非致命差错
		String znResult = param.getString("znResult"); // 1:合格  2:致命差错  3:非致命差错
		boolean isWait = param.getBooleanValue("isWait");
		boolean isCache = param.getBooleanValue("isCache");
		if(StringUtil.isBlank(startDate) || StringUtil.isBlank(endDate)) {
			return EasyResult.fail("查找日期不能为空");
		}
		String entId = getEntId();
		String busiOrderId = getBusiOrderId();
		EasySQL sql = new EasySQL();
		sql.append("SELECT ");
		//列表展示数据
		sql.append(" COALESCE(bo.ORDER_NO, bo1.ORDER_NO, bo2.ORDER_NO) AS ORDER_NO, ");
		sql.append(" COALESCE(bo.CALLER, bo1.CALLER, bo2.CALLER) AS CALLER,");
		sql.append(" COALESCE(bo.CREATE_TIME, bo1.CREATE_TIME, bo2.CREATE_TIME) AS CREATE_TIME,");
		sql.append("obj.CHANNEL_TYPE,task.QC_ORDER,obj.AGENT_ACC,obj.INSPECTOR,obj.CREATE_TIME as QC_CREATE_TIME,  ");
		sql.append("obj.RG_QC_TIME,obj.ZN_QC_TIME,obj.OBJ_STATE, ");
		sql.append("res.EVALUATE_EXPLAIN,obj.SATISF_ID,obj.SATISF_NAME,obj.AGENT_NAME,obj.INSPECTOR_NAME, ");
		// 不能直接 else 3 若分数为空 必须返回空
		sql.append("(CASE WHEN obj.RG_QC_SCORE > 95 THEN 1 WHEN obj.RG_QC_SCORE <= 80 THEN 2 when (obj.RG_QC_SCORE > 80 and obj.RG_QC_SCORE <= 95) then 3 END) as RG_QC_RESULT, " );
		sql.append("(CASE WHEN obj.ZN_QC_SCORE > 95 THEN 1 WHEN obj.ZN_QC_SCORE <= 80 THEN 2 when (obj.ZN_QC_SCORE > 80 and obj.ZN_QC_SCORE <= 95) then 3 END) as ZN_QC_RESULT, " );
		//页面操作相关参数
		sql.append("task.TEMPLATE_ID,res.QC_RESULT_ID,obj.SERIAL_ID,obj.CHANNEL_TYPE CALL_TYPE,task.ZN_CLASS_ID, ");
		sql.append("'' CASE_STATUS,obj.ID OBJ_ID,task.RG_CLASS_ID CLASS_ID,task.EXAM_GROUP_ID,obj.TASK_ID, ");
		sql.append("obj.SOURCE,obj.RG_STATE QC_STATE,res.CACH_STATE,task.TASK_NAME");
		sql.append("FROM "+getTableName("CC_QC_TASK_OBJ obj"));
		sql.append("LEFT JOIN "+getTableName("C_BO_BASE_ORDER bo") +" ON bo.ID = obj.SERIAL_ID ");
		sql.append("LEFT JOIN "+ getTableName("C_BO_BASE_ORDER bo1") + " ON obj.CHANNEL_TYPE = '1' AND bo1.SOURCE_RECORD_ID = obj.SERIAL_ID");
		sql.append("LEFT JOIN "+ getTableName("C_BO_BASE_ORDER bo2") + " ON obj.CHANNEL_TYPE = '2' AND bo2.SOURCE_RECORD_ID = obj.SERIAL_ID");
		sql.append("LEFT JOIN "+getTableName("CC_QC_TASK task") +" ON obj.TASK_ID = task.ID ");
		sql.append("LEFT JOIN "+getTableName("CC_QC_RESULT res") +" ON obj.RG_RESULT_ID = res.QC_RESULT_ID ");
		sql.append("WHERE 1=1 ");
//		sql.append("and bo.PROC_INST_ID IS NOT NULL");
		sql.append(entId, " and obj.ENT_ID = ?");
		sql.append(busiOrderId, " and obj.BUSI_ORDER_ID = ?");
		//质检开始与结束时间
		sql.append(startDate," and obj.CREATE_TIME >= ?");
		sql.append(endDate," and obj.CREATE_TIME <= ?");
		//质检方式
		if (StringUtils.isNotBlank(qcOrder)){
			if (qcOrder.contains("1")){
				qcOrder +=  ",0";
			}
			sql.appendIn(qcOrder.split(","), "and task.QC_ORDER ");
		}
		//工单类型
		if  (StringUtils.isNotBlank(channelType)){
			sql.appendIn(channelType.split(","),  " and obj.CHANNEL_TYPE ");
		}
		//员工姓名
		sql.appendLike(agentAcc, " and obj.AGENT_ACC like ? ");
		//员工姓名
		sql.appendLike(agentName, " and obj.AGENT_NAME like ? ");
		//质检员账号
		sql.appendLike(inspector, " and obj.INSPECTOR like ? ");
		//质检员姓名
		sql.appendLike(inspectorName, " and obj.INSPECTOR_NAME like ? ");
		//质检工单状态
		if (StringUtils.isNotBlank(objState)){
			sql.appendIn(objState.split(","), " and obj.OBJ_STATE ");
		}
		//人工质检结果
		if (StringUtils.isNotBlank(rgResult)){
			EasySQL searchSql = new EasySQL();
			searchSql.append(" and (");
			if (rgResult.contains(Constants.QC_RESULT_PASS)){
				searchSql.append(" obj.RG_QC_SCORE > 95 ");
			}
			if (rgResult.contains(Constants.QC_RESULT_FATAL)){
				if (searchSql.getSQL().contains("RG_QC_SCORE")) searchSql.append(" or ");
				searchSql.append(" obj.RG_QC_SCORE <= 80 ");
			}
			if (rgResult.contains(Constants.QC_RESULT_NOT_FATAL)){
				if (searchSql.getSQL().contains("RG_QC_SCORE")) searchSql.append(" or ");
				searchSql.append(" (obj.RG_QC_SCORE > 80 and obj.RG_QC_SCORE <= 95) ");
			}
			searchSql.append(" )");
			sql.append(searchSql.getSQL());
		}
		//智能质检结果
		if (StringUtils.isNotBlank(znResult)){
			EasySQL searchSql = new EasySQL();
			searchSql.append(" and (");
			if (znResult.contains(Constants.QC_RESULT_PASS)){
				searchSql.append("  obj.ZN_QC_SCORE > 95 ");
			}
			if (znResult.contains(Constants.QC_RESULT_FATAL)){
				if (searchSql.getSQL().contains("ZN_QC_SCORE")) searchSql.append(" or ");
				searchSql.append("  obj.ZN_QC_SCORE <= 80 ");
			}
			if (znResult.contains(Constants.QC_RESULT_NOT_FATAL)){
				if (searchSql.getSQL().contains("ZN_QC_SCORE")) searchSql.append(" or ");
				searchSql.append("  (obj.ZN_QC_SCORE > 80 and obj.ZN_QC_SCORE <= 95) ");
			}
			searchSql.append(" )");
			sql.append(searchSql.getSQL());
		}
		//投诉号码
		sql.appendLike(caller, " and bo.CALLER like ? ");
		//用户评分
		if (StringUtils.isNotBlank(satisfyId)){
			sql.appendIn(satisfyId.split(","), " and obj.SATISF_ID ");
		}
		//待质检清单查询数据
		if (isWait){
			sql.append(" and obj.RG_STATE = 2");
			boolean isAdmin = false;
			isAdmin = getUser().getRoles().stream()
					.anyMatch(role -> "1".equals(role.getRoleType()));
			//非管理员查看本身数据  管理员默认查全部的
			if (!isAdmin){
				sql.append(getUser().getUserAcc()," and obj.INSPECTOR = ?");
			}
		}
		//辅导清单查询数据
		if (isCache){
			//辅导状态
			if (StringUtils.isNotBlank(cachState)){
				sql.appendIn(cachState.split(","), " and res.CACH_STATE ");
			} else {
				sql.append( " and res.CACH_STATE IN ('1','2')");
			}
			//数据必须由坐席结案
			sql.append("and res.APPROVAL_STATE = '5'");
			//需要辅导的数据
//			sql.append("and res.CACH_STATE != '0' ");
			String openCoachScore =  SystemParamUtil.getEntParam(getUser().getSchemaName(), getUser().getEpCode(), getUser().getBusiOrderId(), Constants.APP_NAME, "OPEN_COACH_SCORE");
			if(StringUtil.equals("Y", openCoachScore)) {
				sql.append(Constants.getPassCoachScore()," and res.SCORE <= ?");
			}

		}
		logger.info(CommonUtil.getClassNameAndMethod(this) + "获取质检清单查询sql:"+sql.getSQL() + ",param:"+JSON.toJSONString(sql.getParams()));
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 获取质检清单顶层视图统计指标
	 */
	@InfAuthCheck(resId ={"cc-qc-zjqd","cc-qc-rwzx"},checkType = InfAuthCheck.ResIdCheckType.OR, msg = "您无权访问!")
	@WebControl(name="getAllQualityTopStats", type=Types.RECORD)
	public JSONObject getAllQualityTopStats() {
		JSONObject result = new JSONObject();
		try {
			String entId = getEntId();
			String busiOrderId = getBusiOrderId();

			// 获取本月1号00:00:00
			java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
			java.util.Calendar cal = java.util.Calendar.getInstance();
			cal.set(java.util.Calendar.DAY_OF_MONTH, 1);
			String monthStart = sdf.format(cal.getTime()) + " 00:00:00";
			// 今天23:59:59
			String today = sdf.format(new java.util.Date());
			String monthEnd = today + " 23:59:59";

			// 人工质检统计
			EasySQL rgSql = new EasySQL();
			rgSql.append("SELECT IFNULL(sum(case when ZN_QC_SCORE is not null then 1 else 0 end),0) as RG_TOTAL, ");
			rgSql.append("IFNULL(sum(case when RG_QC_SCORE > 95 then 1 else 0 end),0) as RG_PASS, ");
			rgSql.append("IFNULL(sum(case when RG_QC_SCORE <= 80 then 1 else 0 end),0) as RG_FATAL, ");
			rgSql.append("IFNULL(sum(case when RG_QC_SCORE > 80 and RG_QC_SCORE <= 95 then 1 else 0 end),0) as RG_NOT_FATAL ");
			rgSql.append("FROM " + getTableName("CC_QC_TASK_OBJ"));
			rgSql.append("WHERE RG_STATE IN ('3','4') ");
			rgSql.append(entId, "AND ENT_ID = ?");
			rgSql.append(busiOrderId, "AND BUSI_ORDER_ID = ?");
			rgSql.append(monthStart, "AND CREATE_TIME >= ?");
			rgSql.append(monthEnd, "AND CREATE_TIME <= ?");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "获取人工质检统计sql:"+rgSql.getSQL() + ",param:"+JSON.toJSONString(rgSql.getParams()));
			JSONObject rgObj = queryForRecord(rgSql.getSQL(), rgSql.getParams());
			int rgTotal = rgObj.getJSONObject("data").getIntValue("RG_TOTAL");
			int rgPass = rgObj.getJSONObject("data").getIntValue("RG_PASS");
			int rgFatal = rgObj.getJSONObject("data").getIntValue("RG_FATAL");
			int rgNotFatal = rgObj.getJSONObject("data").getIntValue("RG_NOT_FATAL");
			result.put("rgTotal", rgTotal);
			result.put("rgPass", rgPass);
			result.put("rgPassRate", rgTotal > 0 ? String.format("%.2f", rgPass * 100.0 / rgTotal) : "0.00");
			result.put("rgFatal", rgFatal);
			result.put("rgFatalRate", rgTotal > 0 ? String.format("%.2f", rgFatal * 100.0 / rgTotal) : "0.00");
			result.put("rgNotFatal", rgNotFatal);
			result.put("rgNotFatalRate", rgTotal > 0 ? String.format("%.2f", rgNotFatal * 100.0 / rgTotal) : "0.00");
			//统计人工应质检量
			JSONObject obj = queryForRecord("SELECT count(distinct(USER_ACC)) as RG_COUNT FROM " + getTableName("CC_QC_GROUP_INSPECTOR t1 ")
					+ "JOIN " + getTableName("CC_QC_GROUP t2 ") + "ON t1.EXAM_GROUP_ID = t2.EXAM_GROUP_ID WHERE t1.ENABLE_STATUS = '01'");
			int rgCount = obj.getJSONObject("data").getIntValue("RG_COUNT");
			result.put("rgCount", rgCount * 20);

			// 智能质检统计
			EasySQL znSql = new EasySQL();
			znSql.append("SELECT IFNULL(count(*),0) as ZN_TOTAL, ");
			znSql.append("IFNULL(sum(case when ZN_QC_SCORE > 95 then 1 else 0 end),0) as ZN_PASS, ");
			znSql.append("IFNULL(sum(case when ZN_QC_SCORE <= 80 then 1 else 0 end),0) as ZN_FATAL, ");
			znSql.append("IFNULL(sum(case when ZN_QC_SCORE > 80 and ZN_QC_SCORE <= 95 then 1 else 0 end),0) as ZN_NOT_FATAL ");
			znSql.append("FROM " + getTableName("CC_QC_TASK_OBJ"));
			znSql.append("WHERE ZN_STATE = '4'");
			znSql.append(entId, "AND ENT_ID = ?");
			znSql.append(busiOrderId, "AND BUSI_ORDER_ID = ?");
			znSql.append(monthStart, "AND CREATE_TIME >= ?");
			znSql.append(monthEnd, "AND CREATE_TIME <= ?");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "获取智能质检统计sql:"+znSql.getSQL() + ",param:"+JSON.toJSONString(znSql.getParams()));
			JSONObject znObj = queryForRecord(znSql.getSQL(), znSql.getParams());
			int znTotal = znObj.getJSONObject("data").getIntValue("ZN_TOTAL");
			int znPass = znObj.getJSONObject("data").getIntValue("ZN_PASS");
			int znFatal = znObj.getJSONObject("data").getIntValue("ZN_FATAL");
			int znNotFatal = znObj.getJSONObject("data").getIntValue("ZN_NOT_FATAL");
			result.put("znTotal", znTotal);
			result.put("znPass", znPass);
			result.put("znPassRate", znTotal > 0 ? String.format("%.2f", znPass * 100.0 / znTotal) : "0.00");
			result.put("znFatal", znFatal);
			result.put("znFatalRate", znTotal > 0 ? String.format("%.2f", znFatal * 100.0 / znTotal) : "0.00");
			result.put("znNotFatal", znNotFatal);
			result.put("znNotFatalRate", znTotal > 0 ? String.format("%.2f", znNotFatal * 100.0 / znTotal) : "0.00");

			// 申诉与辅导统计，基于cc_qc_task_obj和cc_qc_result，统计范围以cc_qc_task_obj.CREATE_TIME为准
			// 申诉量：RECONSIDER_FLAG in (1,2,3,4)
			// 申诉通过量：RECONSIDER_FLAG=2
			// 申诉不通过量：RECONSIDER_FLAG=3
			// 辅导量：CACH_STATE in (1,2)（left join cc_qc_result）
			// 未完成辅导量：CACH_STATE=1
			// 已完成辅导量：CACH_STATE=2
			EasySQL reconsiderSql = new EasySQL();
			reconsiderSql.append("SELECT ");
			reconsiderSql.append("IFNULL(sum(case when t1.RECONSIDER_FLAG in (1,2,3,4) then 1 else 0 end),0) as APPEAL_TOTAL, ");
			reconsiderSql.append("IFNULL(sum(case when ((t1.RECONSIDER_FLAG IN (2,4) AND T1.SECOND_RECONSIDER_RESULT='1') " +
					"OR (T1.RECONSIDER_FLAG IN (2,4)  AND T1.ONE_RECONSIDER_RESULT='1' AND T1.SECOND_RECONSIDER_RESULT IS NULL)) " +
					" then 1 else 0 end),0) as APPEAL_PASS, ");
			reconsiderSql.append("IFNULL(sum(case when ((t1.RECONSIDER_FLAG IN (3,4) AND T1.SECOND_RECONSIDER_RESULT='0') " +
					"OR (T1.RECONSIDER_FLAG IN (3,4)  AND T1.ONE_RECONSIDER_RESULT='0' AND T1.SECOND_RECONSIDER_RESULT IS NULL)) " +
					" then 1 else 0 end),0) as APPEAL_FAIL, ");
			reconsiderSql.append("IFNULL(sum(case when t2.CACH_STATE in (1,2) then 1 else 0 end),0) as COACH_TOTAL, ");
			reconsiderSql.append("IFNULL(sum(case when t2.CACH_STATE=1 then 1 else 0 end),0) as COACH_UNFINISHED, ");
			reconsiderSql.append("IFNULL(sum(case when t2.CACH_STATE=2 then 1 else 0 end),0) as COACH_FINISHED ");
			reconsiderSql.append("FROM " + getTableName("CC_QC_TASK_OBJ") + " t1 ");
			reconsiderSql.append("LEFT JOIN " + getTableName("CC_QC_RESULT") + " t2 ON t1.RG_RESULT_ID = t2.QC_RESULT_ID ");
			reconsiderSql.append("WHERE 1=1");
			reconsiderSql.append(entId, "AND t1.ENT_ID = ?");
			reconsiderSql.append(busiOrderId, "AND t1.BUSI_ORDER_ID = ?");
			reconsiderSql.append(monthStart, "AND t1.CREATE_TIME >= ?");
			reconsiderSql.append(monthEnd, "AND t1.CREATE_TIME <= ?");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "获取申诉与辅导统计sql:"+reconsiderSql.getSQL() + ",param:"+JSON.toJSONString(reconsiderSql.getParams()));
			JSONObject reconsiderObj = queryForRecord(reconsiderSql.getSQL(), reconsiderSql.getParams());
			result.put("appealTotal", reconsiderObj.getJSONObject("data").getIntValue("APPEAL_TOTAL"));
			result.put("appealPass", reconsiderObj.getJSONObject("data").getIntValue("APPEAL_PASS"));
			result.put("appealFail", reconsiderObj.getJSONObject("data").getIntValue("APPEAL_FAIL"));
			result.put("coachTotal", reconsiderObj.getJSONObject("data").getIntValue("COACH_TOTAL"));
			result.put("coachUnfinished", reconsiderObj.getJSONObject("data").getIntValue("COACH_UNFINISHED"));
			result.put("coachFinished", reconsiderObj.getJSONObject("data").getIntValue("COACH_FINISHED"));
		} catch (Exception e) {
			logger.error("获取质检清单顶层视图统计指标出现异常:"+ e.getMessage());
			return EasyResult.fail("获取质检清单顶层视图统计指标出现异常");
		}
		return EasyResult.ok(result);
	}

	/**
	 * 通过客诉号码查询相关工单信息 (包含录音信息)
	 */
	@WebControl(name="OrderMessageByPhone", type=Types.RECORD)
	public JSONObject OrderMessageByPhone(){
		try {
			String phone = param.getString("phone");
			String epCode = getEntId();
			if (StringUtils.isBlank(phone)) {
				return EasyResult.fail("号码不能为空");
			}
			// 近1月起止
			java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
			java.util.Calendar cal = java.util.Calendar.getInstance();
			String today = sdf.format(cal.getTime()) + " 23:59:59";
			cal.add(java.util.Calendar.MONTH, -1);
			String lastMonth = sdf.format(cal.getTime()) + " 00:00:00";
			// 本月1号
			cal = java.util.Calendar.getInstance();
			cal.set(java.util.Calendar.DAY_OF_MONTH, 1);
			String monthStart = sdf.format(cal.getTime()) + " 00:00:00";
			// 近1月工单数
			EasySQL countSql = new EasySQL();
			countSql.append("SELECT count(*) as ORDER_COUNT FROM " + getTableName("C_BO_BASE_ORDER"));
			countSql.append(phone,"WHERE CALLER = ?");
			countSql.append(epCode, "AND EP_CODE = ?");
			countSql.append(lastMonth, "AND CREATE_TIME >= ?");
			countSql.append(today, "AND CREATE_TIME <= ?");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "获取工单数量sql:"+countSql.getSQL() + ",param:"+JSON.toJSONString(countSql.getParams()));
			JSONObject countObj = queryForRecord(countSql.getSQL(), countSql.getParams());
			int orderCount = countObj.getJSONObject("data").getIntValue("ORDER_COUNT");
			// 本月首次工单时间
			EasySQL firstSql = new EasySQL();
			firstSql.append("SELECT min(CREATE_TIME) as FIRST_ORDER_TIME FROM " + getTableName("C_BO_BASE_ORDER"));
			firstSql.append(phone,"WHERE CALLER = ?");
			firstSql.append(epCode, "AND EP_CODE = ?");
			firstSql.append(monthStart, "AND CREATE_TIME >= ?");
			firstSql.append(today, "AND CREATE_TIME <= ?");
			JSONObject firstObj = queryForRecord(firstSql.getSQL(), firstSql.getParams());
			logger.info(CommonUtil.getClassNameAndMethod(this) + "获取工单首次时间sql:"+firstSql.getSQL() + ",param:"+JSON.toJSONString(firstSql.getParams()));
			String firstOrderTime = firstObj.getJSONObject("data").getString("FIRST_ORDER_TIME");
			// 本月最后一次归档时间
			EasySQL lastSql = new EasySQL();
			lastSql.append("SELECT max(END_TIME) as LAST_END_TIME FROM " + getTableName("C_BO_BASE_ORDER"));
			lastSql.append(phone,"WHERE CALLER = ?");
			lastSql.append(epCode, "AND EP_CODE = ?");
			lastSql.append(monthStart, "AND END_TIME >= ?");
			lastSql.append(today, "AND END_TIME <= ?");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "获取工单最后一次归档时间sql:"+lastSql.getSQL() + ",param:"+JSON.toJSONString(lastSql.getParams()));
			JSONObject lastObj = queryForRecord(lastSql.getSQL(), lastSql.getParams());
			String lastEndTime = lastObj.getJSONObject("data").getString("LAST_END_TIME");
			JSONObject result = new JSONObject();
			result.put("orderCount", orderCount);
			result.put("firstOrderTime", firstOrderTime);
			result.put("lastEndTime", lastEndTime);
			//直接查询工单相关录音
			if (StringUtils.isBlank(firstOrderTime)){
				// 最近一个月
				firstOrderTime = lastMonth;
			}
			if (StringUtils.isBlank(lastEndTime)){
				lastEndTime = today;
			}
			PhoneCryptor cryptor = PhoneCryptor.getInstance();
			// 一条SQL直接关联两表
			EasySQL sql = new EasySQL();
			sql.append("SELECT t1.SERIAL_ID FROM " + getTableName("CC_CALL_RECORD") + " t1 ");
			sql.append("WHERE 1=1 AND ");
			sql.append(" (t1.CALLER in('"+cryptor.encrypt(epCode, phone, cryptor.BUSI_TYPE_PHONE)+"','"+phone+"')");
			sql.append(" OR t1.CALLED in('"+cryptor.encrypt(epCode, phone, cryptor.BUSI_TYPE_PHONE)+"','"+phone+"'))");
			sql.append(epCode,"AND t1.ENT_ID = ? ");
			sql.append(firstOrderTime,"AND t1.BILL_BEGIN_TIME >= ? ");
			sql.append(lastEndTime,"AND t1.BILL_BEGIN_TIME <= ? ");
			sql.append(" AND t1.CREATE_CAUSE in ('6','8')"); //筛选呼出录音
			sql.append(" AND BILL_TIME != 0 "); //过滤通话时长为0的通话
			logger.info(CommonUtil.getClassNameAndMethod(this) + "获取工单录音信息sql:"+sql.getSQL() + ",param:"+JSON.toJSONString(sql.getParams()));
			JSONObject serialObj = queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			JSONArray objList = serialObj.getJSONArray("data");
			JSONArray serialIdList = new JSONArray();
			objList.forEach(obj -> {
				JSONObject jsonObj = (JSONObject) obj;
				String serialId = jsonObj.getString("SERIAL_ID");
				if (serialId != null && !serialId.isEmpty()) {
					serialIdList.add(serialId);
				}
			});
			result.put("serialIdList", serialIdList);
			return EasyResult.ok(result);
		} catch (Exception e) {
			logger.error("查询工单信息异常" + e.getMessage());
			return EasyResult.fail("查询工单信息异常");
		}
	}

}





