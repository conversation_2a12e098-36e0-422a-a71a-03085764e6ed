package com.yunqu.yc.quality.v3.handler;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.yq.busi.common.dict.DictCache;
import org.apache.commons.lang.time.StopWatch;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.LogUtil;
import com.yq.busi.common.util.SystemParamUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yunqu.yc.quality.base.CommonLogger;
import com.yunqu.yc.quality.base.Constants;
import com.yunqu.yc.quality.base.QueryFactory;
import com.yunqu.yc.quality.enums.ChannelTypeEnum;
import com.yunqu.yc.quality.enums.EmailEnum;
import com.yunqu.yc.quality.enums.ErrorMsgEnum;
import com.yunqu.yc.quality.enums.TrackTypeLog;
import com.yunqu.yc.quality.handler.TaskHandler;
import com.yunqu.yc.quality.model.QcTaskLog;
import com.yunqu.yc.quality.utils.CommonUtils;
import com.yunqu.yc.quality.utils.HttpUtil;
import com.yunqu.yc.quality.utils.LogUtils;
import com.yunqu.yc.quality.utils.QueryUtil;
import com.yunqu.yc.quality.utils.SQLUtil;
import com.yunqu.yc.quality.utils.SchemaUtil;
import com.yunqu.yc.quality.utils.StringUtil;

import cn.hutool.core.util.NumberUtil;
import org.easitline.common.utils.lang.DictUtil;

/**
 * 质检服务Handler
 *
 */
public class QcTaskServiceHandler2 {
	
	private static Logger logger = CommonLogger.getLogger("scan");
	
	private static EasyCache cache = CacheManager.getMemcache();
	
	//全局变量、如果上一个作业任务没有完成则不进入当前任务
	private static boolean IS_RUNNING = false;
	
	public static void scanData(String schema, String entId) {
		
		if(!IS_RUNNING) {
			IS_RUNNING = true;
		}else{
			logger.error("上一个调度正在执行，跳出执行！");
			return ;
		}
		
  		try {
  			if(StringUtil.isBlank(schema) && CommonUtil.isNotBlank(entId)) {
  				schema = SchemaService.findSchemaByEntId(entId);
  			}
  			if(StringUtil.isBlank(schema) || StringUtil.isBlank(entId)) {
  				logger.error("业务库，企业ID为空");
  				return ;
  			}
  			
  			//3.4版本限制了质检任务抽取的数量 超过指定数新增一个任务
  			//先找出正在执行的且数量大于指定值的任务
  			String currTime = DateUtil.getCurrentDateStr();

  			EasySQL sql = new EasySQL("SELECT  sum(t2.QC_COUNT) COUNT, ");
  			sql.append(" t1.ID,t1.PARENT_ID,");
  			sql.append(" min(t1.TASK_NAME) TASK_NAME,");
  			sql.append(" min(t1.START_TIME) START_TIME,");
  			sql.append(" min(t1.END_TIME) END_TIME,");
  			sql.append(" min(t1.ENT_ID) ENT_ID,");
  			sql.append(" min(t1.BUSI_ORDER_ID) BUSI_ORDER_ID,");
  			sql.append(" min(t1.CREATOR) CREATOR,");
  			sql.append(" min(t1.CREATE_TIME) CREATE_TIME,");
  			sql.append(" min(t1.UPDATE_TIME) UPDATE_TIME,");
  			sql.append(" min(t1.UPDATE_ACC) UPDATE_ACC,");
  			sql.append(" min(t1.STATE) STATE,");
  			sql.append(" min(t1.EXAM_GROUP_ID) EXAM_GROUP_ID,");
  			sql.append(" min(t1.QC_PERCENT) QC_PERCENT,");
  			sql.append(" min(t1.CHANNEL_TYPE) CHANNEL_TYPE,");
  			sql.append(" min(t1.EXTRACT_ID) EXTRACT_ID,");
  			sql.append(" min(t1.ZN_CLASS_ID) ZN_CLASS_ID,");
  			sql.append(" min(t1.RG_CLASS_ID) RG_CLASS_ID,");
  			sql.append(" min(t1.QC_ORDER) QC_ORDER,");
  			sql.append(" min(t1.LOAD_DATE) LOAD_DATE,");
  			sql.append(" min(t1.QC_COUNT) QC_COUNT,");
  			sql.append(" min(t1.ZN_COUNT) ZN_COUNT,");
  			sql.append(" min(t1.RG_COUNT) RG_COUNT,");
  			sql.append(" min(t1.PREV_DATE) PREV_DATE,");
  			sql.append(" min(t1.RUNTIME_JSON) RUNTIME_JSON,");
  			sql.append(" min(t1.QC_MIN_NUM) QC_MIN_NUM,"); 
  			sql.append(" min(t1.QC_DISTRI_RULES) QC_DISTRI_RULES,");
  			sql.append(" min(t1.ISTOGROUPLEADER) ISTOGROUPLEADER,");
  			sql.append(" min(t1.AUTO_ASSIGN_TYPE) AUTO_ASSIGN_TYPE,");
  			sql.append(" min(t1.AUTO_ASSIGN_TO_LEADER) AUTO_ASSIGN_TO_LEADER,");
  			sql.append(" min(t1.IS_SELF_EXAMINATION) IS_SELF_EXAMINATION,");
  			sql.append(" min(t1.ORIGINAL_QC_RATE) ORIGINAL_QC_RATE,");
  			sql.append(" min(t1.AGENT_QC_TYPE) AGENT_QC_TYPE,");
  			sql.append(" min(t1.EXTRACT_OTHER_DATA) EXTRACT_OTHER_DATA,");
  		     sql.append(" min(t1.AGENT_QC_DATA_TYPE) AGENT_QC_DATA_TYPE,");
  		     sql.append(" min(t1.EX_JSON) EX_JSON,");
  			sql.append(" case when min(t1.AGENT_QC_TYPE_MINCOUNT) is null then 0 else min(t1.AGENT_QC_TYPE_MINCOUNT) end  as AGENT_QC_TYPE_MINCOUNT,");
  			sql.append(" case when min(t1.AGENT_QC_TYPE_MAXCOUNT) is null then 0 else  min(t1.AGENT_QC_TYPE_MAXCOUNT) end as AGENT_QC_TYPE_MAXCOUNT,");
  			sql.append(" min(t1.TEMPLATE_ID) TEMPLATE_ID "); 
  			sql.append("FROM " + schema + ".CC_QC_TASK t1 ");
  			
  			sql.append(" left join "+ schema +".cc_qc_task_stat t2 ");
  			sql.append(" on t1.ID = t2.TASK_ID ");
  			sql.append(" WHERE 1=1 ");
  			sql.append(" AND t1.STATE='1' ");//任务状态 测试时去掉，方便查找
  			sql.append("N"," AND t1.IS_PARENT=? ");
  			sql.append(entId, " AND t1.ENT_ID=? ");
  			//直接查询时间条件的任务
  			sql.append(currTime," and t1.START_TIME<=?");
  			sql.append(currTime," and (t1.END_TIME>=?");
  			sql.append(" or t1.END_TIME is null or t1.END_TIME='')");
 
  			sql.append(" group by t1.ID,t1.PARENT_ID ");
  			int taskSelectCount = Constants.getTaskSelectCount(schema, entId, SchemaUtil.getCcBusiOrderIdByEntId(entId) );
  			sql.append(taskSelectCount," having sum(t2.QC_COUNT) >= ?");

  			logger.info("查看质检任务是否爆满的sql："+sql.getSQL()+",param:"+JSON.toJSONString(sql.getParams()));
  			EasyQuery query = QueryFactory.getWriteQuery();
			List<JSONObject>  rows = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());

			for(JSONObject row : rows) {		
				String id=row.getString("ID");
				String parentId=row.getString("PARENT_ID");
				if(StringUtils.isBlank(parentId)) {
					parentId=id;
				}
				String newTaskId = RandomKit.randomStr();
				
				int taskInt = query.queryForInt("SELECT COUNT(1) FROM " + schema + ".CC_QC_TASK t1 WHERE PARENT_ID=?"
						, new Object[]{parentId});
				taskInt++;
				logger.info("[质检任务超过了配置数量则新增任务]");
				row.remove("COUNT");
				EasyRecord record = new EasyRecord(schema+".CC_QC_TASK","ID");
				record.set("ID", id);
				record.set("IS_PARENT", "Y");
				record.put("UPDATE_TIME",DateUtil.getCurrentDateStr());
				query.update(record);

				
				row.put("CREATE_TIME",DateUtil.getCurrentDateStr());
				row.put("CREATOR","system");
				row.put("UPDATE_ACC","");
				row.put("UPDATE_TIME","");
				row.put("ID",newTaskId);
				row.put("PARENT_ID",parentId);
				row.put("TASK_NAME",row.getString("TASK_NAME")+"_延伸任务_"+taskInt);
				row.put("STATE","1");
				EasyRecord record2= new EasyRecord(schema+".CC_QC_TASK","ID").setColumns(row);
				record2.set("IS_PARENT", "N");
				query.save(record2);
				
				//关联保存智能分配规则
				sql = new EasySQL();
				sql.append("SELECT * FROM " + schema + ".CC_QC_EXTRACT_COLUMN");
				sql.append("WHERE 1=1");
				sql.append(parentId,"AND EXTRACT_ID = ?");
				sql.append("ORDER BY CREATE_TIME ");
				List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
				if (list!=null && list.size()>0) {
					for (JSONObject extractObj:list) {
						extractObj.put("ID", RandomKit.randomStr());
						extractObj.put("EXTRACT_ID", newTaskId);
						
						record2= new EasyRecord(schema+".CC_QC_EXTRACT_COLUMN","ID").setColumns(extractObj);
						query.save(record2);
					}
				}
				
				//3.4.2 判断是否需要推送智能质检任务
				sendZnTask(newTaskId,"1",row.getString("UPDATE_ACC"),row.getString("ENT_ID"),row.getString("BUSI_ORDER_ID"),schema,query);
				
			}
			String busiOrderId = SchemaUtil.getCcBusiOrderIdByEntId(entId);
			
			//抽取语音
  			QcTaskServiceHandler2.extractRecordToQcObj(schema, entId); 
  			//抽取全媒体
  			QcTaskServiceHandler2.extractMediaToQcObj(schema, entId);
  			
  			//抽取邮件
//  			String openEmail=Constants.getOpenEmailQcBtn(schema,entId,busiOrderId);
//  			logger.info("抽取邮件:"+openEmail);
//			if (StringUtils.equals("Y",openEmail)){
//				QcTaskServiceHandler2.extractEmailToQcObj(schema, entId);
//			}
			//抽取第三方
//			String openThird=Constants.getOpenThirdPartyQcBtn(schema,entId,busiOrderId);
//			logger.info("抽取第三方:"+openThird);
//			if (StringUtils.equals("Y",openThird)){
//				QcTaskServiceHandler2.extractThirdPartyToQcObj(schema, entId);
//			}
			//抽取工单
			String openOrder=Constants.getOpenOrderQcBtn(schema,entId,busiOrderId);
			logger.info("是否开启工单质检:"+openOrder);
			if (StringUtils.equals("Y",openOrder)){
				QcTaskServiceHandler2.extractOrderToQcObj(schema, entId);
			}
  			
  		}catch (Exception e) {
  			logger.error(" 查询企业数据库出错:"+e.getMessage(),e);
		}finally {
			IS_RUNNING = false;
		}
  	}
	
	/**
	 * 推送智能质检任务
	 * @param taskId 任务ID
	 * @param taskStatus 任务状态 0:待启动 1:启动中 2:已暂停 3:已关闭
	 */
	private static JSONObject sendZnTask(String taskId,String taskStatus,String userAcc,String entId,String busiOrderId,String schema,EasyQuery query){
		JSONObject resultJson = new JSONObject();
		resultJson.put("state", 0);
		resultJson.put("msg", "未开启智能质检任务创建");
		//获取智能质检对接的版本 3.3与3.4存在差异
		String znVersion = SystemParamUtil.getEntParam(schema, entId, busiOrderId
				, Constants.APP_NAME, "ZN_VERSION");
		String operateTaskUrl = Constants.getOperateTaskUrl();
		logger.info("获取智能质检对接的版本:" + znVersion);
		logger.info("获取推送智能质检任务Url:" + operateTaskUrl);
		if (StringUtils.equals("3.3", znVersion) || StringUtils.isBlank(znVersion) || StringUtils.isBlank(operateTaskUrl)) {
			return resultJson;
		}else{
			try {
				JSONObject taskJSON = query.queryForRow("SELECT * FROM "+schema+".CC_QC_TASK WHERE ID= ?", new Object[]{taskId}, new JSONMapperImpl());
				//字典:QC_ORDER 0：人工、智能质检并行 1:先智能质检再人工质检 2：仅人工质检
				String qcOrder = taskJSON.getString("QC_ORDER");
				//智能质检模型ID
				String znClassId = taskJSON.getString("ZN_CLASS_ID");
				//渠道类型，1：语音，2：全媒体，3-邮件 9-自定义 4-工单
				String channelType = taskJSON.getString("CHANNEL_TYPE");
				
				//判断任务是否开启智能质检
				if (StringUtils.equals("2", qcOrder)) {
					return resultJson;
				}
				//判断是否语音、在线渠道
				if (StringUtils.equals("1", channelType) || StringUtils.equals("2", channelType)) {
					//判断智能质检模型ID是否为空
					if (StringUtils.isBlank(znClassId)) {
						return resultJson;
					}
					//调用接口创建智能质检任务 
					JSONObject paramJson = new JSONObject();
					paramJson.put("serialId", RandomKit.uuid());// 消息流水号,32位UDDI，全局唯一，响应消息中带回
					paramJson.put("timestamp", DateUtil.getTimestamp(DateUtil.getCurrentDateStr()));
					
					JSONObject dataJson = new JSONObject();
					dataJson.put("busiId",Constants.getBusiId());//企业客服默认007
					dataJson.put("entId", entId);
					dataJson.put("taskId", taskId);
					dataJson.put("taskName", taskJSON.getString("TASK_NAME"));
					dataJson.put("modelId", znClassId);
					if (StringUtils.equals("1", channelType)) {
						dataJson.put("busiType", "0");
					}else if (StringUtils.equals("2", channelType)) {
						dataJson.put("busiType", "1");
					}
					
					if (StringUtils.equals("1", taskStatus)) {
						dataJson.put("taskStatus", "0");
					}else if (StringUtils.equals("2", taskStatus)) {
						dataJson.put("taskStatus", "1");
					}else if (StringUtils.equals("3", taskStatus)) {
						dataJson.put("taskStatus", "2");
					}
					dataJson.put("userCode", userAcc);
					paramJson.put("data", dataJson);
					logger.info("[QcTaskServiceHandler2] -> sendZnTask 推送智能质检任务Url:"+operateTaskUrl+",param:"+paramJson);
					HttpResp resp = HttpUtil.sendPost(operateTaskUrl, paramJson.toJSONString(),
							"UTF-8", "application/json", HttpUtil.TYPE_JSON);
					if (resp!=null && resp.getCode() == 200) {
						JSONObject respJson = JSON.parseObject(resp.getResult());
						logger.info("[QcTaskServiceHandler2] -> sendZnTask 推送智能质检任务Url:"+operateTaskUrl+",param:"+paramJson + ",respJson=" + respJson);
						String desc = respJson.getString("desc");
						if (!StringUtils.equals("000", respJson.getString("result"))) {
							resultJson.put("state", 0);
						}
						resultJson.put("msg", desc);
					}else{
						String result = resp == null ? "null" : JSON.toJSONString(resp);
						logger.error("[QcTaskServiceHandler2] -> znClassDict 获取智能质检模型Url:"+operateTaskUrl+",param:"+paramJson + ",result=" + result);
						resultJson.put("state", 0);
						resultJson.put("msg", "创建智能质检任务失败!请联系管理员");
					}
					
				}
				return resultJson;
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
				resultJson.put("state", 0);
				resultJson.put("msg", "创建智能质检任务失败!请联系管理员");
				return resultJson;
			}
		}
	}
	
	/**
	 * 抽取话单数据到质检任务对象表
	 * @return
	 */
	public static void extractRecordToQcObj(String schema, String entId) {
		getQcTaskList(null,schema, entId, "");
	}
	
	/**
	 * 抽取全媒体记录到质检任务对象表
	 * @return
	 */
	public static void extractMediaToQcObj(String schema, String entId) {
		getQcTaskListByMedia(null,schema, entId, "");
	}

	/**
	 * 抽取邮件数据到质检任务对象表
	 * @return
	 */
	public static void extractEmailToQcObj(String schema, String entId) {
		getQcTaskListByEmail(null,schema, entId, "");
	}

	/**
	 * 抽取第三方数据到质检任务对象表
	 * @return
	 */
	public static void extractThirdPartyToQcObj(String schema, String entId) {
		getQcTaskListByThirdParty(null,schema, entId, "");
	}
	
	/**
	 * 抽取工单数据到质检任务对象表
	 * @return
	 */
	public static void extractOrderToQcObj(String schema, String entId) {
		getQcTaskListByOrder(null,schema, entId, "");
	}
	
	/**
	 * 
	 *  执行语音任务抽取
	 *	1、查询正在启动中，且未过期的任务
	 *  2、根据任务抽取数据，写入抽取对象、抽取日志
	 * @return
	 */
	public static JSONObject getQcTaskList(UserModel user, String schema, String entId, String runTaskId) {
		String currTime = DateUtil.getCurrentDateStr();
		EasySQL sql = new EasySQL("SELECT t1.*, t2.EXECUTE_SQL,t2.APPEAL_SOURCE FROM " + schema + ".CC_QC_TASK t1 ");
		sql.append(" LEFT JOIN " + schema + ".CC_QC_EXTRACT t2 ON t1.EXTRACT_ID=t2.ID ");
		sql.append(" WHERE 1=1 ");
		sql.append(runTaskId, " AND t1.ID=? ");
		sql.append(" AND t1.STATE='1' ");//任务状态 测试时去掉，方便查找
		sql.append(" AND t1.CHANNEL_TYPE='1' ");//渠道为语音
		sql.append(" AND t1.IS_PARENT='N' ");//不是父任务
		sql.append(entId, " AND t1.ENT_ID=? ");
		//直接查询时间条件的任务
		sql.append(currTime," and t1.START_TIME<=?");
		sql.append(currTime," and (t1.END_TIME>=?");
		sql.append(" or t1.END_TIME is null or t1.END_TIME='')");

		//取消数量限制
		EasyQuery query = QueryFactory.getWriteQuery();
		try {
			if(ServerContext.isDebug()) {
				logger.info(" 抽取定时任务查找语音质检任务：sql:" + 
						sql.getSQL() + "param:" + JSON.toJSONString(sql.getParams()));
			}
			List<EasyRow>  rows = query.queryForList(sql.getSQL(), sql.getParams());
			if(CommonUtil.listIsNotNull(rows)) {
				List<String> taskIds = new ArrayList<String>();
				StringBuffer taskIdSb = new StringBuffer();
				//计时器
				StopWatch sw = new StopWatch();
				StringBuffer t = new StringBuffer(DateUtil.getCurrentDateStr("YYYYMMddHHmm"));
				t.setCharAt(t.length() - 1, '0');
				String cachePrefix = t.toString() + "_task_log_id_";
				
				//每一个任务的抽取规则不同
				for (EasyRow row : rows) {
					JSONObject logParam = row.toJSONObject();
					logParam.put("USER_MODEL",user);
					String taskId = row.getColumnValue("ID");
					String taskName = row.getColumnValue("TASK_NAME");
					String channelType = row.getColumnValue("CHANNEL_TYPE");
					String executeSql = row.getColumnValue("EXECUTE_SQL");
					entId = row.getColumnValue("ENT_ID");
					String busiOrderId = row.getColumnValue("BUSI_ORDER_ID");
					String extractId = row.getColumnValue("EXTRACT_ID");
					String extractOtherData = row.getColumnValue("EXTRACT_OTHER_DATA");
					// 工单来源
					String appealSource = row.getColumnValue("APPEAL_SOURCE");

					//记录语音渠道任务执行日志
					QcTaskLog taskLog = new QcTaskLog();
					taskLog.setTaskId(taskId);
					taskLog.setExecTime(currTime);
					taskLog.setEntId(entId);
					taskLog.setBusiOrderId(busiOrderId);
					taskLog.setCreateTime(currTime);
					taskLog.setExecResult(QcTaskLog.EXEC_SUCC);

					//任务执行ID
					String taskLogKey = cachePrefix + taskId;
					String taskLogId = cache.get(taskLogKey);
					taskLogId = StringUtils.isBlank(taskLogId) ? RandomKit.randomStr() : taskLogId;
					logParam.put("TASK_EXEC_ID",taskLogId);
					logParam.put("USER_MODEL",user);

					LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
							,"语音质检任务开始执行抽取...","语音质检任务开始执行抽取...");

					//用于拼接查找条件，需要用在需要用在抽取数据可统计坐席数据两个地方，这里先不拼查找的字段，实际使用的时候再拼接
					EasySQL sql1 = new EasySQL(" FROM " + CommonUtil.getTableName(schema, "CC_CALL_RECORD T1"));
					//添加工单来源条件
					if (StringUtils.isNotBlank(appealSource)) {
						sql1.append(" LEFT JOIN " + CommonUtil.getTableName(schema, "C_BO_BASE_ORDER O"));
						sql1.append(" ON T1.SERIAL_ID = O.SOURCE_RECORD_ID");
						sql1.append("LEFT JOIN " + CommonUtil.getTableName(schema, "C_BOX_MUSIC_STANDARD_BASE B"));
						sql1.append("ON O.ID = B.M_ID");
					}

					//外部模块质检数据
					boolean hasOutSql = QueryUtil.addOutQcSql(schema, entId, busiOrderId, extractId, query, sql1);
					if(!hasOutSql) {
						sql1.append("WHERE 1=1 ");
					}
					//添加工单来源条件
					if (StringUtils.isNotBlank(appealSource)) {
						sql1.appendIn(appealSource.split(",")," AND B.APPEAL_SOURCE ");
					}

					//当断当前时间是否在当前任务配置的可运行时间内
					if(!TaskHandler.nowInRunTime(taskId, entId, busiOrderId)){
						LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
								,"质检任务不在可运行时间内,跳过定时抽取！","质检任务不在可运行时间内，跳过定时抽取,请检查任务运行时间配置task="+taskId);
						logger.warn("任务不在可运行时间内,请检查配置task=" + taskId);

						taskLog.setExecResult(QcTaskLog.EXEC_FAIL);
						taskLog.appendExecDesc("当前时间,已配置成不允许执行,无法执行抽取,请检查质检参数配置.");
						//存储日志信息
						saveTaskLogRecord(schema, taskLogKey, taskLog, logParam);
						continue;
					}
					sw.reset();
					sw.start();
					//任务上次请求时间
					String loadDate = row.getColumnValue("LOAD_DATE");
					
					logger.info("开始执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],上次抽取的数据时间点:"+loadDate);
					taskLog.appendExecDesc("任务抽取开始,上次抽取时间点为:"+loadDate);
					
					if(CommonUtil.isNotBlank(taskId)) {
						taskIds.add(taskId);
				      if (taskIdSb.length()!=0) {
				       taskIdSb.append(",");
				      }
				      taskIdSb.append("'").append(taskId).append("'");
				     }
					
					//如果不是语音的渠道类型或者抽取sql是空，则跳过
					EasySQL exeSql = QueryUtil.getExeSql(schema, entId, busiOrderId, extractId, "T1.",null);
					executeSql = exeSql.getSQL();
					if((!"1".equals(channelType)) || StringUtils.isBlank(executeSql)) {
						LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
								,"质检任务抽取话单Sql为空,跳过定时抽取！","质检任务抽取话单Sql为空,跳过定时抽取,请检查抽检规则配置task="+taskId);
						logger.error(" 抽取话单sql为空！ taskId=" + taskId);
						continue;
					}
					sql1.append(entId, " AND T1.ENT_ID=? ");
					sql1.append(busiOrderId, " AND T1.BUSI_ORDER_ID=? ");
					if(!StringUtils.contains(executeSql, "CLEAR_CAUSE")) {
						//判断抽取规则是否含有CLEAR_CAUSE结束原因条件，如果未指定结束原因，语音只抽取通话成功-CLEAR_CAUSE=0
						sql1.append(" AND T1.CLEAR_CAUSE = 0 ");
					}
					//当前任务请求时间
					String tempDate = DateUtil.addMinute(DateUtil.TIME_FORMAT, currTime, -10);
					String startLoadTime = "";
					String endLoadTime = tempDate;
					//获取动态时间规则的时间范围   20220418抽取时间过滤需要改为 BILL_BEGIN_TIME，BILL_END_TIME无索引，也可能为空
					JSONObject dynamicTimeRange = TaskHandler.getDynamicTimeRange(extractId, schema, query);
					if(dynamicTimeRange != null) {
						logger.info("BILL_END_TIME -> one");
						//存在动态时间
						startLoadTime = dynamicTimeRange.getString("startLoadTime");
						endLoadTime = dynamicTimeRange.getString("endLoadTime");
						sql1.append(startLoadTime, " AND T1.BILL_END_TIME >=? ");
						sql1.append(endLoadTime, " AND T1.BILL_END_TIME <=? ");
					}else
					if(StringUtils.isBlank(loadDate)) {
						logger.info("BILL_END_TIME -> two");
						//获取抽取规则里的时间规则
						startLoadTime = TaskHandler.getTaskMinTimeCall(extractId, entId, busiOrderId, schema, query);
						logger.info("startLoadTime :"+startLoadTime);
						if(CommonUtil.isNotBlank(startLoadTime)) {
							String lastMoth = DateUtil.addMonth(DateUtil.TIME_FORMAT, startLoadTime, 1);
							if(StringUtils.compareIgnoreCase(tempDate, lastMoth) > 0) {
								endLoadTime = lastMoth;
							}
						}
						//3.1#20210824-1优化定时任务抽取sql
						sql1.append(startLoadTime, " AND T1.BILL_END_TIME >? ");
						//如果不存在上次请求时间，首次请求，挂机时间小于当前质检任务开始时间
						sql1.append(endLoadTime, " AND T1.BILL_END_TIME <? ");
					}else {
						logger.info("BILL_END_TIME -> three");
						startLoadTime = loadDate;
						String lastMoth = DateUtil.addMonth(DateUtil.TIME_FORMAT, startLoadTime, 1);
						if(StringUtils.compareIgnoreCase(tempDate, lastMoth) > 0) {
							endLoadTime = lastMoth;
						}
						//如果存在上次请求时间，为不重复请求数据，挂机时间小于当前时间，还需要大于上次请求时间
						sql1.append(startLoadTime, " AND T1.BILL_END_TIME >? ");
						sql1.append(endLoadTime, " AND T1.BILL_END_TIME <? ");
					}
					//记录抽取开始时间
					taskLog.setExtractBeginTime(startLoadTime);
//					taskLog.setExtractEndTime(endLoadTime);
					//是否抽取其他任务内数据
					if (StringUtils.equals("N", extractOtherData) || StringUtils.isBlank( extractOtherData)) {
						sql1.append("AND (T1.QC_STATE IS NULL OR T1.QC_STATE = 0)");
						taskLog.appendExecDesc("本次仅仅抽取未被其他任务抽取过的通话记录.");
					}
					
					logger.info("执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],本次抽取数据范围:["+startLoadTime+" - "+endLoadTime+"]");
					logParam.put("CONTENT","执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],本次抽取数据范围:["+startLoadTime+" - "+endLoadTime+"]");
					//CREATE_CAUSE，通过配置读取，不配置查询所有，也就是不设置查询条件，
					//呼叫创建原因 1外线呼入 2IVR转入 3席间转移呼入 4IVR咨询呼入 5席间咨询呼入 6呼出 8预拨号呼出 9呼叫前转呼入 10转移呼入 14席间呼入 29 席间呼出
					SQLUtil.addExecuteSql(sql1, exeSql);

					int size = 0;
					Map<String, String> commParam = new HashMap<String, String>();
					commParam.put("schema", schema);
					commParam.put("currTime", currTime);
					commParam.put("endLoadTime", endLoadTime);
					commParam.put("hasDynamicTime", dynamicTimeRange != null ? "1":"0");
					//名单抽取
					size = extractCallOriginalData(sql1, row, commParam, query ,logParam,taskLog);
					logger.info("执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],size:["+size+"]");

					long duration = 0;
					try {
						sw.split();//获取耗时
						duration = sw.getSplitTime();//单位毫秒
					}catch (Exception e) {
					}
					//抽取数量
					taskLog.setExtractNum(size);
					taskLog.setDuration(duration);
					
					//获取接收到智能质检结果的数量
					if(CommonUtil.isNotBlank(taskId)) {
						String taskResultNumKey = "TASK_CALLBACK_NUM_" + taskId;
						Integer taskResultNum = cache.get(taskResultNumKey);
						if(taskResultNum != null) {
							taskLog.setZnResultNum(taskResultNum);
							cache.delete(taskResultNumKey);
						}
					}
					//存储日志信息
					saveTaskLogRecord(schema, taskLogKey, taskLog, logParam);

					//添加追踪日志 --流程内记录CONTENT追踪内容
					logParam.put("DURATION",duration +" ms");
					LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId,TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam);

					logParam.put("CREATE_TIME", DateUtil.addSecond(DateUtil.TIME_FORMAT,EasyCalendar.newInstance().getDateTime("-"),5));
					LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId,TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
							,"语音质检任务抽取结束,共耗时 "+duration+" ms...","语音质检任务抽取结束,共耗时 "+duration+" ms...");
				}
				//更新所有任务的上次执行时间
				if(CommonUtil.listIsNotNull(taskIds)) {
					query.execute("UPDATE " + schema + ".cc_qc_task set PREV_DATE=? "
							+ " WHERE ID in (" +  taskIdSb.toString() +  ")"
							, new Object[]{DateUtil.getCurrentDateStr()});
				}
			} else {
				LogUtils.addTaskTrackLogAutoType(user,schema,entId,SchemaUtil.getCcBusiOrderIdByEntId(entId), TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,new JSONObject()
						,"未查到任何在执行时间段内的语音任务,不执行任何操作.","未查到任何在执行时间段内的语音任务,不执行任何操作.");
				if(ServerContext.isDebug()) {
					logger.info(" 未查到任何在执行时间段内的语音任务,不执行任何操作."); 
				}
			}
			String updateTaskSql = "UPDATE "+schema+".CC_QC_TASK SET STATE = '3' WHERE START_TIME<=? AND STATE!='3' AND END_TIME IS NOT NULL AND END_TIME<=?";
			query.execute(updateTaskSql, currTime,currTime);
			if(ServerContext.isDebug()) {
				logger.info(" 找出状态不为已关闭且实际已过期的任务,将其状态设置为已关闭! sql >> " + updateTaskSql + ", param >> [" + currTime + ", " + currTime + "]");
			}
		} catch (Exception e) {
			logger.error( "查找话单质检任务失败，原因："+e.getMessage(),e);
			LogUtil.addSystemErrorLog(UserModel.getSystemUser(), Constants.APP_NAME, "【数据自动抽取到质检池】抽取异常:"+e.getMessage(), e,logger);
		}
		return null;
	}

	/**
	 * 抽取原始数据
	 * @param extract
	 * @param task
	 * @param commParam
	 * @param query
	 * @return
	 */
	private static int extractCallOriginalData(EasySQL extract, EasyRow task, Map<String, String> commParam, EasyQuery query ,JSONObject logParam,QcTaskLog taskLog) throws Exception {
		String taskId = task.getColumnValue("ID");
		String taskName = task.getColumnValue("TASK_NAME");
		String hasDynamicTime = commParam.get("hasDynamicTime");
		String entId = task.getColumnValue("ENT_ID");
		String busiOrderId = task.getColumnValue("BUSI_ORDER_ID");
		String schema = commParam.get("schema");
		if(Constants.isOpenQcOnlyOne()) {
			//如果开启每条记录只质检一次,只取未质检状态的数据
			extract.append("AND (T1.QC_STATE IS NULL OR T1.QC_STATE = 0)");
			taskLog.appendExecDesc("由于质检参数配置里开启了一条记录只运行抽取一次,本次将不再抽取已被抽取过的语音记录.");
		}
		StringBuffer selecterSql = new StringBuffer("SELECT T1.SERIAL_ID,T1.BILL_BEGIN_TIME,T1.BILL_END_TIME,T1.AGENT_ID,T1.RECORD_FILE,T1.CALLER,T1.CALLED,T1.PHONE_NUM,T1.SATISF_ID ");
		selecterSql.append(" " + extract.getSQL());
		selecterSql.append(" order by T1.BILL_END_TIME ");
		logger.info(" 执行话单数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],抽取话单数据 sql:" + selecterSql.toString() + "param:" + JSON.toJSONString(extract.getParams()));
		//查找抽取数据
		List<JSONObject>  serialIds = query.queryForList(selecterSql.toString(), extract.getParams(), new JSONMapperImpl());
		logger.info(" 执行话单数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],抽取数据 size:"+serialIds.size() );
        taskLog.appendExecDesc("本次执行抽取到的数量:"+( serialIds==null ? 0 :serialIds.size()) );
		//添加追踪日志
		logParam.put("EXEC_SQL",selecterSql.toString() + ";param:" + JSON.toJSONString(extract.getParams()));
		LogUtils.addTaskTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
				,">>>执行话单数据抽取...","");
		return addCallTaskObj(serialIds, task, commParam, false, null, query ,logParam,taskLog);
	}
	
	/**
	 * 将原始通话记录添加到质检对象表
	 * @param serialList
	 * @param task
	 * @param commParam
	 * @param isLimit
	 * @param angetNumMap
	 * @return
	 */
	public static int addCallTaskObj(List<JSONObject>  serialList, EasyRow task, Map<String, String> commParam, boolean isLimit
			, Map<String, Integer> angetNumMap, EasyQuery query ,JSONObject logParam,QcTaskLog taskLog) throws Exception {
		//抽取成功的总量
		int size = 0;
		//找不到坐席信息的总量
		int noAgentSize = 0;
		//找不到id的总量
		int noIdSize = 0;
		//非质检组里的坐席数据总量
		int noQcGroupAgent = 0;
		//已被该任务抽取过的总量
		int alreadyQcNum = 0;
		//已被该任务的父任务抽取过的总量
		int alreadyQcByParentNum = 0;

		String taskId = task.getColumnValue("ID");
		String taskName = task.getColumnValue("TASK_NAME");
		String channelType = task.getColumnValue("CHANNEL_TYPE");
		String entId = task.getColumnValue("ENT_ID");
		String busiOrderId = task.getColumnValue("BUSI_ORDER_ID");
		String examGroupId = task.getColumnValue("EXAM_GROUP_ID");
		String schema = commParam.get("schema");
		String currTime = commParam.get("currTime");
		String endLoadTime = commParam.get("endLoadTime");
		String maxEndTime = "";
		List<JSONObject> extObjectList = new ArrayList<>();
		Map<String ,Integer> errMap = new HashMap<>();
		//查询对应该质检任务对应的质检组是否有质检对象，没有则默认按抽取全部人员
		boolean isExtractAll = isExtractAllObj(query, schema, examGroupId);
		if(CommonUtil.listIsNotNull(serialList)) {
			Set<String> setIds = new HashSet<String>();
			//查询任务的所有质检坐席
			Set<String> angetAccSet = null;
			if(!isLimit) {
				angetAccSet = getTaskAgnets(examGroupId, schema, entId, busiOrderId, query);
			}else {
				angetAccSet = angetNumMap.keySet();
			}
			for (JSONObject easyRow : serialList) {
				logger.info("easyRow:" + easyRow);
				
				String serialId = easyRow.getString("SERIAL_ID");
				String endTime = easyRow.getString("BILL_END_TIME");
				String beginTime = easyRow.getString("BILL_BEGIN_TIME");
				//录音AGENT_ID存的是ID
				String agentId = easyRow.getString("AGENT_ID");
				String satisfId = easyRow.getString("SATISF_ID");
				JSONObject agent = CacheUtil.getCcUserCache().getCache(entId, busiOrderId, agentId);
				JSONObject taskObjLog = LogUtils.getTaskObjTrackLogParam(task, easyRow ,agent ,logParam);
				String agentInfo = agent == null ? "" : agent.getString("USERNAME")+"("+agent.getString("USER_ACCT")+")";
				String recordfile = easyRow.getString("RECORD_FILE");
				if(StringUtils.compareIgnoreCase(endTime, maxEndTime) > 0) {
					maxEndTime = endTime;
				}
				
				
				if(agent == null) {
					noAgentSize ++;
					errMap.put(ErrorMsgEnum.NOT_AGENT.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.NOT_AGENT.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:找不到坐席【"+agentInfo+"】数据;请检查坐席是否为空或者是否在质检组内,如坐席账号变更请更新质检组内坐席数据, AGENT_ID=" + agentId);
					LogUtils.addTaskObjTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					logger.info("找不到坐席数据entId=" + entId +", busiOrderId=" + busiOrderId + ", agentId=" + agentId);
					continue;
				}
				if(StringUtils.isBlank(serialId) || StringUtils.isBlank(recordfile) || setIds.contains(serialId)) {
					noIdSize ++;
					errMap.put(ErrorMsgEnum.DATA_EXCEP.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.DATA_EXCEP.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:该录音记录数据异常;SERIAL_ID=" + serialId +", RECORD_FILE=" + recordfile);
					logger.info("抽取失败:该录音记录数据异常;SERIAL_ID=" + serialId +", RECORD_FILE=" + recordfile);
					LogUtils.addTaskObjTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					continue;
				}

				if (!isExtractAll){
					//如果质检组不是空，并且坐席不存在质检组里，不质检这条数据
					if(angetAccSet.size() > 0 && !angetAccSet.contains(agentId)) {
						noQcGroupAgent ++;
						errMap.put(ErrorMsgEnum.AGENT_NO_IN_GROUP.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.AGENT_NO_IN_GROUP.getCode())+"")+1);
						taskObjLog.put("CONTENT","抽取失败:坐席【"+agentInfo+"】不存在质检组里,不质检这条录音记录。");
						logger.info("抽取失败:坐席【"+agentInfo+"】不存在质检组里,不质检这条录音记录。");
						LogUtils.addTaskObjTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
						continue;
					}
				}

				//判断话单是否抽取过
				if(existQcObj(taskId, serialId, schema, query)) {
					alreadyQcNum ++ ;
					errMap.put(ErrorMsgEnum.IS_CHECKED.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.IS_CHECKED.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:该录音记录已被抽取过。");
					logger.info("抽取失败:该录音记录已被抽取过。");
					//LogUtils.addTaskObjTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					continue;
				}
			
				//判断话单是否被父任务抽取过
			    if(existQcParentObj(taskId, serialId, schema, query,task.getColumnValue("PARENT_ID")).size()>0) {
					alreadyQcByParentNum ++ ;
					errMap.put(ErrorMsgEnum.IS_CHECKED.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.IS_CHECKED.getCode())+"")+1);
			     	taskObjLog.put("CONTENT","抽取失败:该录音记录已被父任务抽取过。");
			     	logger.info("抽取失败:该录音记录已被父任务抽取过。");
			     	//LogUtils.addTaskObjTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
			     	continue;
			    }
			    
				setIds.add(serialId);
				EasyRecord er = new EasyRecord(schema + ".CC_QC_TASK_OBJ", "ID");
				er.set("ID", RandomKit.randomStr());
				er.set("SERIAL_ID", serialId);
				er.set("ENT_ID", entId);
				er.set("BUSI_ORDER_ID", busiOrderId);
				er.set("CREATE_TIME", currTime);
				er.set("UPDATE_TIME", currTime);
				er.set("TASK_ID", taskId);
				er.set("CHANNEL_TYPE", channelType == null ? "" : channelType);
				er.set("ZN_STATE", Constants.QC_STATE_EXTRACT);
				er.set("RG_STATE", Constants.QC_STATE_EXTRACT);
				//服务时间,通话或会话开始时间,格式如2020-12-12 12:00:00
				er.set("SERVICE_TIME", beginTime);
				// 20250603存储满意度评价id
				er.set("SATISF_ID", satisfId);
				String satisfName = DictCache.getDictVal(entId, "VOICE_SATISF", satisfId);
				er.set("SATISF_NAME", satisfName);
				er.set("OBJ_STATE", Constants.QC_GLOBAL_STATE_ZN);
				er.set("DATE_ID", CommonUtil.parseInteger(currTime.substring(0,10).replaceAll("-", "")));
				//20220421
				if(agent !=null){
					er.set("AGENT_ID", agent.getString("USER_ID"));
					er.set("AGENT_ACC", agent.getString("USER_ACCT"));
					er.set("AGENT_NAME", agent.getString("USERNAME"));
					er.set("AGENT_DEPT_CODE", agent.getString("DEPT_CODE"));
				}
				query.save(er);
				//将录音改为已抽取状态
				EasyRecord updateCall = new EasyRecord(schema + ".CC_CALL_RECORD", "SERIAL_ID");
				updateCall.setPrimaryValues(serialId);
				updateCall.set("QC_STATE", Constants.QC_STATE_EXTRACT);
				query.update(updateCall);
				
				size += 1;
				taskObjLog.put("CONTENT","抽取成功:该录音记录被抽取到【"+taskName+"】当中。");
				LogUtils.addTaskObjTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
				extObjectList.add(easyRow);
			}
			if(size == 0) {
				maxEndTime = endLoadTime;
			}
			QueryUtil.uodateQcTaskLoadTime(schema, taskId, maxEndTime, query);
			taskLog.setExtractEndTime(maxEndTime);
			logParam.put("CONTENT",logParam.getString("CONTENT")+">>>质检任务抽取成功,过滤不符合条件的数据后,共抽取["+size+"条]数据");

			taskLog.appendExecDesc("本次质检任务抽取后剔除的数据总量:"+(noAgentSize+noIdSize+noQcGroupAgent+alreadyQcNum+alreadyQcByParentNum)
					+",包含: 记录找不到坐席的总量:"+noAgentSize
					+",记录无ID的总量:"+noIdSize
					+",非质检组里的坐席数据总量:"+noQcGroupAgent
					+",已被该任务抽取过的总量:"+alreadyQcNum
					+",已被该任务的父任务抽取过的总量:"+alreadyQcByParentNum
					+"<br>"
			);
		} else {
			//抽取不到数据时更新数据加载时间到endLoadTime
			QueryUtil.uodateQcTaskLoadTime(schema, taskId, endLoadTime, query);
			taskLog.setExtractEndTime(endLoadTime);
			logParam.put("CONTENT",">>>质检任务抽取完成,该抽取范围内没有符合条件的数据。");
		}
		taskLog.appendExecDesc("本次抽取后最终总量:"+size);

		LogUtils.addErrMsgLog(logParam, entId, busiOrderId, schema, errMap);
		return size;
	}
	
	/**
	 * 将原始工单记录添加到质检对象表
	 * @param serialList
	 * @param task
	 * @param commParam
	 * @param isLimit
	 * @param angetNumMap
	 * @return
	 */
	public static int addOrderTaskObj(List<JSONObject>  serialList, EasyRow task, Map<String, String> commParam, boolean isLimit
			, Map<String, Integer> angetNumMap, EasyQuery query ,JSONObject logParam,QcTaskLog taskLog) throws Exception {
		//抽取成功的总量
		int size = 0;
		//找不到坐席信息的总量
		int noAgentSize = 0;
		//找不到id的总量
		int noIdSize = 0;
		//非质检组里的坐席数据总量
		int noQcGroupAgent = 0;
		//已被该任务抽取过的总量
		int alreadyQcNum = 0;
		//已被该任务的父任务抽取过的总量
		int alreadyQcByParentNum = 0;
		
		
		String taskId = task.getColumnValue("ID");
		String taskName = task.getColumnValue("TASK_NAME");
		String channelType = task.getColumnValue("CHANNEL_TYPE");
		String entId = task.getColumnValue("ENT_ID");
		String busiOrderId = task.getColumnValue("BUSI_ORDER_ID");
		String examGroupId = task.getColumnValue("EXAM_GROUP_ID");
		String schema = commParam.get("schema");
		String currTime = commParam.get("currTime");
		String endLoadTime = commParam.get("endLoadTime");
		String maxEndTime = "";
		List<JSONObject> extObjectList = new ArrayList<>();
		Map<String ,Integer> errMap = new HashMap<>();
		//查询对应该质检任务对应的质检组是否有质检对象，没有则默认按抽取全部人员
		boolean isExtractAll = isExtractAllObj(query, schema, examGroupId);
		logger.info("[serialList]:"+serialList.size());
		if(CommonUtil.listIsNotNull(serialList)) {
			Set<String> setIds = new HashSet<String>();
			//查询任务的所有质检坐席
			Set<String> angetAccSet = null;
			if(!isLimit) {
				angetAccSet = getTaskAgnets(examGroupId, schema, entId, busiOrderId, query, "AGENT_ACC");
			}else {
				angetAccSet = angetNumMap.keySet();
			}
			for (JSONObject easyRow : serialList) {
				String serialId = easyRow.getString("ID");
				String doneTime = easyRow.getString("END_TIME");
				//处理人账号
				String agentId = easyRow.getString("ORDER_CHARGE");
				JSONObject agent = CacheUtil.getCcUserCache().getCache(entId, busiOrderId, agentId);
				JSONObject taskObjLog = LogUtils.getTaskObjTrackLogParam(task, easyRow ,agent ,logParam);
				String agentInfo = agent == null ? "" : agent.getString("USERNAME")+"("+agent.getString("USER_ACCT")+")";
				if(StringUtils.compareIgnoreCase(doneTime, maxEndTime) > 0) {
					maxEndTime = doneTime;
				}
				
				if(agent == null) {
					noAgentSize ++;
					errMap.put(ErrorMsgEnum.NOT_AGENT.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.NOT_AGENT.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:找不到坐席【"+agentInfo+"】数据;请检查坐席是否为空或者是否在质检组内,如坐席账号变更请更新质检组内坐席数据, AGENT_ID=" + agentId);
					LogUtils.addTaskObjTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					logger.info("找不到坐席数据entId=" + entId +", busiOrderId=" + busiOrderId + ", agentId=" + agentId);
					continue;
				}
				if(StringUtils.isBlank(serialId) || setIds.contains(serialId)) {
					noIdSize ++;
					errMap.put(ErrorMsgEnum.DATA_EXCEP.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.DATA_EXCEP.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:该工单记录数据异常;SERIAL_ID=" + serialId );
					logger.info("抽取失败:该工单记录数据异常;SERIAL_ID:" + serialId);
					LogUtils.addTaskObjTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					continue;
				}

				if (!isExtractAll){
					//如果质检组不是空，并且坐席不存在质检组里，不质检这条数据
					if(angetAccSet.size() > 0 && !angetAccSet.contains(agentId)) {
						noQcGroupAgent ++;
						errMap.put(ErrorMsgEnum.AGENT_NO_IN_GROUP.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.AGENT_NO_IN_GROUP.getCode())+"")+1);
						taskObjLog.put("CONTENT","抽取失败:坐席【"+agentInfo+"】不存在质检组里,不质检这条工单记录。");
						logger.info("抽取失败:坐席【"+agentInfo+"】不存在质检组里,不质检这条工单记录");
						LogUtils.addTaskObjTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
						continue;
					}
				}

				//判断工单是否抽取过
				if(existQcObj(taskId, serialId, schema, query)) {
					alreadyQcNum ++ ;
					errMap.put(ErrorMsgEnum.IS_CHECKED.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.IS_CHECKED.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:该工单记录已被抽取过。");
					logger.info("抽取失败:该工单记录已被抽取过。");
					//LogUtils.addTaskObjTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					continue;
				}
				
				// 判断工单是否被父任务抽取过
				if (existQcParentObj(taskId, serialId, schema, query, task.getColumnValue("PARENT_ID")).size() > 0) {
					alreadyQcByParentNum ++ ;
					errMap.put(ErrorMsgEnum.IS_CHECKED.getCode(),
							CommonUtil.parseInt(errMap.get(ErrorMsgEnum.IS_CHECKED.getCode()) + "") + 1);
					taskObjLog.put("CONTENT", "抽取失败:该工单记录已被父任务抽取过。");
					logger.info("抽取失败:该工单记录已被父任务抽取过。");
					// LogUtils.addTaskObjTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId,
					// TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					continue;
				}

				setIds.add(serialId);
				EasyRecord er = new EasyRecord(schema + ".CC_QC_TASK_OBJ", "ID");
				er.set("ID", RandomKit.randomStr());
				er.set("SERIAL_ID", serialId);
				er.set("ENT_ID", entId);
				er.set("BUSI_ORDER_ID", busiOrderId);
				er.set("CREATE_TIME", currTime);
				er.set("UPDATE_TIME", currTime);
				er.set("TASK_ID", taskId);
				er.set("CHANNEL_TYPE", channelType == null ? "" : channelType);
				er.set("ZN_STATE", Constants.QC_STATE_EXTRACT);
				er.set("RG_STATE", Constants.QC_STATE_EXTRACT);
				//服务时间,通话或会话开始时间,格式如2020-12-12 12:00:00
				er.set("SERVICE_TIME", doneTime);
				er.set("OBJ_STATE", Constants.QC_GLOBAL_STATE_ZN);
				er.set("DATE_ID", CommonUtil.parseInteger( currTime.substring(0,10).replaceAll("-", "")));
				//20220421
				if(agent !=null){
					er.set("AGENT_ID", agent.getString("USER_ID"));
					er.set("AGENT_ACC", agent.getString("USER_ACCT"));
					er.set("AGENT_NAME", agent.getString("USERNAME"));
					er.set("AGENT_DEPT_CODE", agent.getString("DEPT_CODE"));
				}
				query.save(er);
				
				//将工单状态修改为已抽取
				EasyRecord updateCall = new EasyRecord(schema + ".C_BO_BASE_ORDER","ID");
				updateCall.setPrimaryValues(serialId);
				updateCall.set("QC_STATE",Constants.QC_STATE_EXTRACT);
				query.update(updateCall);
				
				updateCall = new EasyRecord(schema + ".C_BO_BASE_ORDER_HIS","ID");
				updateCall.setPrimaryValues(serialId);
				updateCall.set("QC_STATE",Constants.QC_STATE_EXTRACT);
				query.update(updateCall);
				
				
				size += 1;
				taskObjLog.put("CONTENT","抽取成功:该工单记录被抽取到【"+taskName+"】当中。");
				LogUtils.addTaskObjTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
				extObjectList.add(easyRow);
			}
			if(size == 0) {
				maxEndTime = endLoadTime;
			}
			QueryUtil.uodateQcTaskLoadTime(schema, taskId, maxEndTime, query);
			taskLog.setExtractEndTime(maxEndTime);
			logParam.put("CONTENT",logParam.getString("CONTENT")+">>>质检任务抽取成功,过滤不符合条件的数据后,共抽取["+size+"条]数据");

			taskLog.appendExecDesc("本次质检任务抽取后剔除的数据总量:"+(noAgentSize+noIdSize+noQcGroupAgent+alreadyQcNum+alreadyQcByParentNum)
					+",包含: 记录找不到坐席的总量:"+noAgentSize
					+",记录无ID的总量:"+noIdSize
					+",非质检组里的坐席数据总量:"+noQcGroupAgent
					+",已被该任务抽取过的总量:"+alreadyQcNum
					+",已被该任务的父任务抽取过的总量:"+alreadyQcByParentNum
					+"<br>"
					);
		} else {
			//抽取不到数据时更新数据加载时间到endLoadTime
			QueryUtil.uodateQcTaskLoadTime(schema, taskId, endLoadTime, query);
			taskLog.setExtractEndTime(endLoadTime);
			logParam.put("CONTENT",">>>质检任务抽取完成,该抽取范围内没有符合条件的数据。");
		}
		
		taskLog.appendExecDesc("本次抽取后最终总量:"+size);

		LogUtils.addErrMsgLog(logParam, entId, busiOrderId, schema, errMap);
		return size;
	}



	/**
	 * 判断是否已经抽取到质检对象表
	 * @param taskId
	 * @param serialId
	 * @param schema
	 * @param query
	 * @return
	 */
	public static boolean existQcObj(String taskId, String serialId, String schema, EasyQuery query) throws Exception {
		String existsql = "select count(1) from "+ schema + ".CC_QC_TASK_OBJ"
				+ " where SERIAL_ID=? AND TASK_ID=?";
		return query.queryForExist(existsql, new Object[]{serialId, taskId});
	}
	
	public static Set<String> getTaskAgnets(String examGroupId, String schema, String entId, String busiOrderId, EasyQuery query) throws Exception{
		return getTaskAgnets(examGroupId, schema, entId, busiOrderId, query, "AGENT_ID");
	}
	
	private static List<EasyRow> existQcParentObj(String taskId, String serialId, String schema, EasyQuery query,String parentId) throws Exception {
		  
		  EasySQL sql=new EasySQL("select * from "+ schema + ".CC_QC_TASK_OBJ" );
		  sql.append(" where 1=1");
		  sql.append(serialId," and SERIAL_ID=? ",false);
		  if(StringUtils.isNotBlank(parentId)) {
		   String[] paramValues=new String[] {taskId,parentId};
		   sql.appendIn(paramValues, " and TASK_ID ");
		  }else {
		   sql.append(taskId," and TASK_ID=? ",false);
		  }
		  logger.info("sql"+sql.getSQL()+","+JSON.toJSONString(sql.getParams()));
		  return query.queryForList(sql.getSQL(), sql.getParams());
		  
		 }
	
	/**
	 * 查询任务的所有质检坐席
	 * @param examGroupId 质检组id
	 * @param schema
	 * @param entId
	 * @param busiOrderId
	 * @param query
	 * @param key 取值的字段名
	 * @return
	 */
	public static Set<String> getTaskAgnets(String examGroupId, String schema, String entId, String busiOrderId, EasyQuery query, String key) throws Exception{
		try {
			logger.info("查询任务的所有质检坐席开始----schema:"+schema);
			EasySQL agentSql = new EasySQL("SELECT AGENT_ID,AGENT_ACC FROM " + schema
					+ ".CC_QC_GROUP_AGENT T WHERE T.ENT_ID=? AND T.BUSI_ORDER_ID=? AND ENABLE_STATUS='01'");
			agentSql.append("AND T.EXAM_GROUP_ID=?");
			logger.info("查询任务的所有质检坐席sql>"+agentSql.getSQL());
			List<JSONObject> agentList = query.queryForList(agentSql.getSQL(), new String[] {entId, busiOrderId, examGroupId}, new JSONMapperImpl());
			logger.info("查询任务的所有质检坐席agentList>"+agentList);
			if(CommonUtil.listIsNotNull(agentList)) {
				Set<String> angetAccSet = new HashSet<String>();
				agentList.forEach((json) -> {
					String agentAcc = json.getString(key);
					if(CommonUtil.isNotBlank(agentAcc)) {
						angetAccSet.add(agentAcc);
					}
				});
				return angetAccSet;
			}
		}catch (Exception e){
			logger.error("查询任务的所有质检坐席异常："+e.getMessage(),e);
		}
		return Collections.emptySet();
	}
	
	/**
	 * 执行全媒体任务抽取
	 * 1、查询正在启动中，且未过期的任务
	 * 2、根据任务抽取数据，写入抽取对象、抽取日志
	 * @param schema
	 * @return
	 */
	public static JSONObject getQcTaskListByMedia(UserModel user,String schema, String entId, String runTaskId) {
		String currTime = DateUtil.getCurrentDateStr();
		EasySQL sql = new EasySQL("SELECT t1.*, t2.EXECUTE_SQL,T2.ONLY_VALID,T2.APPEAL_SOURCE FROM " + schema + ".CC_QC_TASK t1 ");
		sql.append(" LEFT JOIN " + schema + ".CC_QC_EXTRACT t2 ON t1.EXTRACT_ID=t2.ID ");
		sql.append(" WHERE 1=1 ");
		sql.append(runTaskId, " AND t1.ID=? ");//任务
		sql.append("1", " AND t1.STATE=? ");//任务状态 0:待启动 1:启动中 2:已暂停 3:已关闭
		sql.append("2", " AND t1.CHANNEL_TYPE=? ");//渠道为全媒体
		sql.append(" AND t1.IS_PARENT='N' ");//不是父任务
		sql.append(entId, " AND t1.ENT_ID=? ");
		sql.append(currTime, " AND t1.START_TIME <= ? ");
		sql.append(currTime, " AND (t1.END_TIME >=? OR t1.END_TIME is null)");
		EasyQuery query = QueryFactory.getWriteQuery();
		try {
			if(ServerContext.isDebug()) {
				logger.info(" 查询正在启动中，且未过期的全媒体质检任务：sql:" +
						sql.getSQL() + "param:" + JSON.toJSONString(sql.getParams()));
			}
			List<EasyRow>  rows = query.queryForList(sql.getSQL(), sql.getParams());
			if(CommonUtil.listIsNotNull(rows)) {
				List<String> taskIds = new ArrayList<String>();
				StringBuffer taskIdSb = new StringBuffer();
				//计时器
				StopWatch sw = new StopWatch();
				StringBuffer t = new StringBuffer(DateUtil.getCurrentDateStr("YYYYMMddHHmm"));
				t.setCharAt(t.length() - 1, '0');
				String cachePrefix = t.toString() + "_task_log_id_";
				//对每一个任务执行数据抽取:每一个任务的抽取规则不同
				for (EasyRow row : rows) {
					JSONObject logParam = row.toJSONObject();
					String taskId = row.getColumnValue("ID");
					String taskName = row.getColumnValue("TASK_NAME");
					String channelType = row.getColumnValue("CHANNEL_TYPE");
					String executeSql = row.getColumnValue("EXECUTE_SQL");
					entId = row.getColumnValue("ENT_ID");
					String busiOrderId = row.getColumnValue("BUSI_ORDER_ID");
					String extractId = row.getColumnValue("EXTRACT_ID");
					String extractOtherData = row.getColumnValue("EXTRACT_OTHER_DATA");
					String appealSource = row.getColumnValue("APPEAL_SOURCE");
					//记录全媒体渠道任务执行日志
					QcTaskLog taskLog = new QcTaskLog();
					taskLog.setTaskId(taskId);
					taskLog.setExecTime(currTime);
					taskLog.setEntId(entId);
					taskLog.setBusiOrderId(busiOrderId);
					taskLog.setCreateTime(currTime);
					taskLog.setExecResult(QcTaskLog.EXEC_SUCC);
					
					//任务上次抽取数据时间
					String loadDate = row.getColumnValue("LOAD_DATE");
					//任务执行ID
					String taskLogKey = cachePrefix + taskId;
					String taskLogId = cache.get(taskLogKey);
					taskLogId = StringUtils.isBlank(taskLogId) ? RandomKit.randomStr() : taskLogId;
					logParam.put("TASK_EXEC_ID",taskLogId);

					LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
							,"全媒体质检任务开始执行抽取...","全媒体质检任务开始执行抽取...");
					
					//当断当前时间是否在当前任务配置的可运行时间内
					if(!TaskHandler.nowInRunTime(taskId, entId, busiOrderId)){
						LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
								,"质检任务不在可运行时间内,跳过定时抽取！","质检任务不在可运行时间内,跳过定时抽取,请检查任务运行时间配置task="+taskId);
						logger.warn("任务不在可运行时间内,请检查配置task=" + taskId);

						taskLog.setExecResult(QcTaskLog.EXEC_FAIL);
						taskLog.appendExecDesc("当前时间,已配置成不允许执行,无法执行抽取,请检查质检参数配置.");
						//存储日志信息
						saveTaskLogRecord(schema, taskLogKey, taskLog, logParam);
						continue;
					}
					sw.reset();
					sw.start();
					logger.info("开始执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],上次抽取的数据时间点:"+loadDate);
					taskLog.appendExecDesc("任务抽取开始,上次抽取时间点为:"+loadDate);
					
					if(CommonUtil.isNotBlank(taskId)) {
						taskIds.add(taskId);
				      if (taskIdSb.length()!=0) {
				       taskIdSb.append(",");
				      }
				      taskIdSb.append("'").append(taskId).append("'");
				     }
					
					
					// 用于拼接查找条件，需要用在需要用在抽取数据可统计坐席数据两个地方，这里先不拼查找的字段，实际使用的时候再拼接
					EasySQL sql1 = new EasySQL(" FROM "  + CommonUtil.getTableName(schema, "CC_MEDIA_RECORD T1"));
					if (StringUtils.isNotBlank(appealSource)) {
						sql1.append(" LEFT JOIN " + CommonUtil.getTableName(schema, "C_BO_BASE_ORDER O"));
						sql1.append(" ON T1.SERIAL_ID = O.SOURCE_RECORD_ID");
						sql1.append("LEFT JOIN " + CommonUtil.getTableName(schema, "C_BOX_MUSIC_STANDARD_BASE B"));
						sql1.append("ON O.ID = B.M_ID");
					}
					
					EasySQL exeSql = QueryUtil.getExeSql(schema, entId, busiOrderId, extractId, "T1.",sql1);
					executeSql = exeSql.getSQL();
					//如果不是全媒体的渠道类型或者抽取sql是空，则跳过
					if((!"2".equals(channelType)) || StringUtils.isBlank(executeSql)) {
						LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
							,"质检任务抽取全媒体Sql为空,跳过定时抽取！","质检任务抽取全媒体Sql为空,跳过定时抽取,请检查抽检规则配置task="+taskId);
						logger.error(" 执行数据抽取失败,任务名称["+taskName+"],任务ID["+taskId+"],原因:不是全媒体任务 或 抽取全媒体sql为空!" );
						continue;
					}
					
					
					boolean hasOutSql = QueryUtil.addOutQcSql(schema, entId, busiOrderId, extractId, query, sql1);
					if(!hasOutSql) {
						sql1.append("where 1=1 ");
					}
					//添加工单来源条件
					if (StringUtils.isNotBlank(appealSource)) {
						sql1.appendIn(appealSource.split(",")," AND B.APPEAL_SOURCE ");
					}
					
					sql1.append(entId, " and T1.ENT_ID=? ");
					sql1.append(busiOrderId, " and T1.BUSI_ORDER_ID=? ");
					
					//本次抽取会话记录的默认截止时间，10分钟之前，即默认只抽10分钟之前的记录
					String tempDate = DateUtil.addMinute(DateUtil.TIME_FORMAT, currTime, -10);
					String startLoadTime = "";
					String endLoadTime = tempDate;
					
					//获取动态时间规则的时间范围
					JSONObject dynamicTimeRange = TaskHandler.getDynamicTimeRange(extractId, schema, query);
					if(dynamicTimeRange != null) {
						//存在动态时间
						startLoadTime = dynamicTimeRange.getString("startLoadTime");
						endLoadTime = dynamicTimeRange.getString("endLoadTime");
						sql1.append(startLoadTime, " AND T1.END_TIME >=? ");
						sql1.append(endLoadTime, " AND T1.END_TIME <=? ");
					} else
					if(StringUtils.isBlank(loadDate)) {
						//该任务之前没有执行过，上次执行时间为空,此时抽取 会话记录表里最早一条记录+1个月内的数据
						startLoadTime = TaskHandler.getTaskMinTimeForMedia(extractId, entId, busiOrderId, schema, query);
						if(CommonUtil.isNotBlank(startLoadTime)) {
							String lastMoth = DateUtil.addMonth(DateUtil.TIME_FORMAT, startLoadTime, 1);
							if(StringUtils.compareIgnoreCase(tempDate, lastMoth) > 0) {
								endLoadTime = lastMoth;
							}
						}
						//如果不存在上次请求时间，首次请求，挂机时间小于当前质检任务开始时间
						sql1.append(startLoadTime, " AND T1.END_TIME >? ");
						sql1.append(endLoadTime, " AND T1.END_TIME <? ");
					}else {
						startLoadTime = loadDate;
						String lastMoth = DateUtil.addMonth(DateUtil.TIME_FORMAT, startLoadTime, 1);
						if(StringUtils.compareIgnoreCase(tempDate, lastMoth) > 0) {
							endLoadTime = lastMoth;
						}
						//如果存在上次请求时间，为不重复请求数据，挂机时间小于当前时间，还需要大于上次请求时间
						sql1.append(startLoadTime, " AND T1.END_TIME >? ");
						sql1.append(endLoadTime, " AND T1.END_TIME <? ");
					}
					//记录抽取开始时间
					taskLog.setExtractBeginTime(startLoadTime);
					
					//是否抽取其他任务内数据
					if (StringUtils.equals("N", extractOtherData)) {
						sql1.append("AND (T1.QC_STATE IS NULL OR T1.QC_STATE = 0)");
						taskLog.appendExecDesc("本次仅仅抽取未被其他任务抽取过的全媒体.");
					}
					
					logger.info("执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],本次抽取数据范围:["+startLoadTime+" - "+endLoadTime+"]");
					logParam.put("CONTENT","执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],本次抽取数据范围:["+startLoadTime+" - "+endLoadTime+"]");
					//对于全媒体，如果任务里指定了结束原因，则按指定的结束原因抽取，如果未指定结束原因，则不指定该条件。
					SQLUtil.addExecuteSql(sql1, exeSql);
					// 查找坐席原始数据抽取率(为空不限制) 
					
					int size = 0;
					Map<String, String> commParam = new HashMap<String, String>();
					commParam.put("schema", schema);
					commParam.put("currTime", currTime);
					commParam.put("endLoadTime", endLoadTime);
					commParam.put("hasDynamicTime", dynamicTimeRange != null ? "1":"0");
					//名单抽取
					size = extractMediaOriginalData(sql1, row, commParam, query ,logParam,taskLog);
					
					//抽取数量
					taskLog.setExtractNum(size);

					long duration = 0;
					try {
						sw.split();//获取耗时
						duration = sw.getSplitTime();
					}catch (Exception e) {
					}
					taskLog.setDuration(duration);

					//获取接收到智能质检结果的数量
					if(CommonUtil.isNotBlank(taskId)) {
						String taskResultNumKey = "TASK_CALLBACK_NUM_" + taskId;
						Integer taskResultNum = cache.get(taskResultNumKey);
						if(taskResultNum != null) {
							taskLog.setZnResultNum(taskResultNum);
							cache.delete(taskResultNumKey);
						}
					}

					//存储日志信息
					saveTaskLogRecord(schema,taskLogKey,taskLog,logParam);

					//添加追踪日志 --流程内记录CONTENT追踪内容
					logParam.put("DURATION",duration +" ms");
					LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId,TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam);

					logParam.put("CREATE_TIME", DateUtil.addSecond(DateUtil.TIME_FORMAT,EasyCalendar.newInstance().getDateTime("-"),5));
					LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId,TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
							,"全媒体质检任务抽取结束,共耗时 "+duration+" ms...","全媒体质检任务抽取结束,共耗时 "+duration+" ms...");
				}
				
				//更新所有任务的上次执行时间
				if(CommonUtil.listIsNotNull(taskIds)) {
					query.execute("UPDATE " + schema + ".cc_qc_task set PREV_DATE=? "
					         + " WHERE ID in (" + taskIdSb.toString()+  ")"
					       , new Object[]{DateUtil.getCurrentDateStr()});
				}
			}else{
				LogUtils.addTaskTrackLogAutoType(user,schema,entId,SchemaUtil.getCcBusiOrderIdByEntId(entId), TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,new JSONObject()
						,"未查到任何在执行时间段内的全媒体任务,不执行任何操作.","未查到任何在执行时间段内的全媒体任务,不执行任何操作.");
				if(ServerContext.isDebug()) {
					logger.info(" 未查到任何在执行时间段内的全媒体任务,不执行任何操作."); 
				}
			}
			String updateTaskSql = "UPDATE "+schema+".CC_QC_TASK SET STATE = '3' WHERE START_TIME<=? AND STATE!='3' AND END_TIME IS NOT NULL AND END_TIME<=?";
			query.execute(updateTaskSql, currTime,currTime);

			if(ServerContext.isDebug()) {
				logger.info(" 找出状态不为已关闭且实际已过期的任务,将其状态设置为已关闭! sql >> " + updateTaskSql + ", param >> [" + currTime + ", " + currTime + "]");
			}
		} catch (Exception e) {
			logger.error("执行全媒体质检任务失败，原因："+e.getMessage(),e);
		}
		return null;
	}

	/**
	 * 执行第三方任务抽取
	 * 1、查询正在启动中，且未过期的任务
	 * 2、根据任务抽取数据，写入抽取对象、抽取日志
	 * @param schema
	 * @param runTaskId 任务ID
	 * @return
	 */
	public static JSONObject getQcTaskListByThirdParty(UserModel user,String schema, String entId, String runTaskId) {
		String currTime = DateUtil.getCurrentDateStr();
		EasySQL sql = new EasySQL("SELECT t1.*, t2.EXECUTE_SQL,T2.ONLY_VALID FROM " + schema + ".CC_QC_TASK t1 ");
		sql.append(" LEFT JOIN " + schema + ".CC_QC_EXTRACT t2 ON t1.EXTRACT_ID=t2.ID ");
		sql.append(" WHERE 1=1 ");
		sql.append(runTaskId, " AND t1.ID=? ");//任务
		sql.append("1", " AND t1.STATE=? ");//任务状态 0:待启动 1:启动中 2:已暂停 3:已关闭
		sql.append(" AND t1.IS_PARENT='N' ");//不是父任务
		sql.append(ChannelTypeEnum.THIRD.getCode(), " AND t1.CHANNEL_TYPE=? ");//渠道为第三方
		sql.append(entId, " AND t1.ENT_ID=? ");
//		sql.append(currTime, " AND t1.START_TIME <= ? ");
//		sql.append(currTime, " AND (t1.END_TIME >=? OR t1.END_TIME is null)");
		EasyQuery query = QueryFactory.getWriteQuery();
		try {
			if(ServerContext.isDebug()) {
				logger.info(" 查询正在启动中，且未过期的第三方质检任务：sql:" +
						sql.getSQL() + "param:" + JSON.toJSONString(sql.getParams()));
			}
			List<EasyRow>  rows = query.queryForList(sql.getSQL(), sql.getParams());
			if(CommonUtil.listIsNotNull(rows)) {
				List<String> taskIds = new ArrayList<String>();
				StringBuffer taskIdSb = new StringBuffer();
				
				//计时器
				StopWatch sw = new StopWatch();
				StringBuffer t = new StringBuffer(DateUtil.getCurrentDateStr("YYYYMMddHHmm"));
				t.setCharAt(t.length() - 1, '0');
				String cachePrefix = t.toString() + "_task_log_id_";
				//对每一个任务执行数据抽取:每一个任务的抽取规则不同
				for (EasyRow row : rows) {
					JSONObject logParam = row.toJSONObject();
					String taskId = row.getColumnValue("ID");
					String taskName = row.getColumnValue("TASK_NAME");
					String channelType = row.getColumnValue("CHANNEL_TYPE");
					String executeSql = row.getColumnValue("EXECUTE_SQL");
					entId = row.getColumnValue("ENT_ID");
					String busiOrderId = row.getColumnValue("BUSI_ORDER_ID");
					String extractId = row.getColumnValue("EXTRACT_ID");
					String templateId = row.getColumnValue("TEMPLATE_ID");
					//cc_qc_third_record 没有status字段去记录是否已经抽取，默认会抽取其他任务里已经被抽取的数据
					String extractOtherData = row.getColumnValue("EXTRACT_OTHER_DATA");

					//记录第三方抽取执行日志
					QcTaskLog taskLog = new QcTaskLog();
					taskLog.setTaskId(taskId);
					taskLog.setExecTime(currTime);
					taskLog.setCreateTime(currTime);
					taskLog.setExecResult(QcTaskLog.EXEC_SUCC);
					taskLog.setEntId(entId);
					taskLog.setBusiOrderId(busiOrderId);

					//任务执行ID
					String taskLogKey = cachePrefix + taskId;
					String taskLogId = cache.get(taskLogKey);
					taskLogId = StringUtils.isBlank(taskLogId) ? RandomKit.randomStr() : taskLogId;
					logParam.put("TASK_EXEC_ID",taskLogId);

					LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
							,"第三方质检任务开始执行抽取...","第三方质检任务开始执行抽取...");

					String startTime = row.getColumnValue("START_TIME");
					String endTime = row.getColumnValue("END_TIME");
					if (!(DateUtil.compareDate(currTime,startTime,DateUtil.TIME_FORMAT) >= 0 && (DateUtil.compareDate(currTime,endTime,DateUtil.TIME_FORMAT) <= 0 || StringUtils.isBlank(endTime)))){
						LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
								,"质检任务不在启动时间内,跳过定时抽取！","质检任务不在启动时间内,跳过定时抽取,请检查任务运行时间配置task="+taskId);
						logger.warn("质检任务不在启动时间内,跳过定时抽取,请检查配置task=" + taskId);

						taskLog.setExecResult(QcTaskLog.EXEC_FAIL);
						taskLog.appendExecDesc("当前时间,不在质检任务的开始时间和结束时间内,无法执行抽取.");
						//存储日志信息
						saveTaskLogRecord(schema, taskLogKey, taskLog, logParam);
						continue;
					}

					// 仅抽取有效数据Y是，N否
					String onlyValid = row.getColumnValue("ONLY_VALID");
					//任务上次抽取数据时间
					String loadDate = row.getColumnValue("LOAD_DATE");
					//当断当前时间是否在当前任务配置的可运行时间内
					if(!TaskHandler.nowInRunTime(taskId, entId, busiOrderId)){
						LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
								,"质检任务不在可运行时间内,跳过定时抽取！","质检任务不在可运行时间内，跳过定时抽取,请检查任务运行时间配置task="+taskId);
						logger.warn("任务不在可运行时间内,请检查配置task=" + taskId);

						taskLog.setExecResult(QcTaskLog.EXEC_FAIL);
						taskLog.appendExecDesc("当前时间,已配置成不允许执行,无法执行抽取,请检查质检参数配置.");
						//存储日志信息
						saveTaskLogRecord(schema, taskLogKey, taskLog, logParam);
						continue;
					}
					sw.reset();
					sw.start();
					logger.info("开始执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],上次抽取的数据时间点:"+loadDate);
					taskLog.appendExecDesc("任务抽取开始,上次抽取时间点为:"+loadDate);

					if(CommonUtil.isNotBlank(taskId)) {
						taskIds.add(taskId);
						if (taskIdSb.length()!=0) {
							taskIdSb.append(",");
						}
						taskIdSb.append("'").append(taskId).append("'");
					}
					EasySQL exeSql = QueryUtil.getThirdPartyExeSql(schema, entId, busiOrderId, extractId, "T1.");
					executeSql = exeSql.getSQL();
					//要改动的！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
					if((!String.valueOf(ChannelTypeEnum.THIRD.getCode()).equals(channelType)) || StringUtils.isBlank(executeSql)) {
						LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
								,"质检任务抽取第三方Sql为空,跳过定时抽取！","质检任务抽取第三方Sql为空,跳过定时抽取,请检查抽检规则配置task="+taskId);
						logger.error(" 执行数据抽取失败,任务名称["+taskName+"],任务ID["+taskId+"],原因:不是第三方任务 或 抽取第三方sql为空!" );
						continue;
					}


					// 用于拼接查找条件，需要用在需要用在抽取数据可统计坐席数据两个地方，这里先不拼查找的字段，实际使用的时候再拼接
					EasySQL sql1 = new EasySQL(" FROM "  + CommonUtil.getTableName(schema, "cc_qc_third_record T1"));

					//外部的，不需要本系统坐席账号的查询，不提供
					//sql1.append(" left join CC_USER T11 on T1.HANDLE_ACC = T11.USER_ACCT AND T1.ENT_ID = T11.ENT_ID");

					//不提供外部模块
					//boolean hasOutSql = QueryUtil.addOutQcSql(schema, entId, busiOrderId, extractId, query, sql1);
					//if(!hasOutSql) {
					//sql1.append("where 1=1 ");
					//}
					sql1.append(" where 1=1 ");

					logger.info("执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"]");
					logParam.put("CONTENT","执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"]");
					SQLUtil.addExecuteSql(sql1, exeSql);
					
					//要拼接一个是那个自定义模板的数据池
					sql1.append(templateId," AND TEMPLATE_ID = ? ");

					//本次抽取会话记录的默认截止时间，10分钟之前，即默认只抽10分钟之前的记录
					String tempDate = DateUtil.addMinute(DateUtil.TIME_FORMAT, currTime, -10);
					String startLoadTime = "";
					String endLoadTime = tempDate;

					//获取动态时间规则的时间范围
					JSONObject dynamicTimeRange = TaskHandler.getDynamicTimeRange(extractId, schema, query);
					if(dynamicTimeRange != null) {
						logger.info("第三方渠道抽取数据时间抽取1");
						//存在动态时间
						startLoadTime = dynamicTimeRange.getString("startLoadTime");
						endLoadTime = dynamicTimeRange.getString("endLoadTime");
						sql1.append(endLoadTime, " AND T1.CREATE_TIME <=? ");
						sql1.append(startLoadTime, " AND T1.CREATE_TIME >=? ");
					} else if(StringUtils.isBlank(loadDate)) {
						//该任务之前没有执行过，上次执行时间为空,此时抽取 会话记录表里最早一条记录+1个月内的数据
						logger.info("第三方渠道抽取数据时间抽取2");
						startLoadTime = TaskHandler.getTaskMinTimeForThirdParty(templateId,extractId, entId, busiOrderId, schema, query);
						if(CommonUtil.isNotBlank(startLoadTime)) {
							String lastMoth = DateUtil.addMonth(DateUtil.TIME_FORMAT, startLoadTime, 1);
							if(StringUtils.compareIgnoreCase(tempDate, lastMoth) > 0) {
								endLoadTime = lastMoth;
							}
						}
						//如果不存在上次请求时间，首次请求，挂机时间小于当前质检任务开始时间
						sql1.append(endLoadTime, " AND T1.CREATE_TIME <=? ");
						sql1.append(startLoadTime, " AND T1.CREATE_TIME >=? ");
					}else {
						logger.info("第三方渠道抽取数据时间抽取3");
						startLoadTime = loadDate;
						String lastMoth = DateUtil.addMonth(DateUtil.TIME_FORMAT, startLoadTime, 1);
						if(StringUtils.compareIgnoreCase(tempDate, lastMoth) > 0) {
							endLoadTime = lastMoth;
						}
						//如果存在上次请求时间，为不重复请求数据，挂机时间小于当前时间，还需要大于上次请求时间
						sql1.append(endLoadTime, " AND T1.CREATE_TIME <=? ");
						sql1.append(startLoadTime, " AND T1.CREATE_TIME >=? ");
					}
					logger.info(CommonUtil.getClassNameAndMethod(QcTaskServiceHandler2.class)+"执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],本次抽取数据范围:["+startLoadTime+" - "+endLoadTime+"]");
					logParam.put("CONTENT","执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],本次抽取数据范围:["+startLoadTime+" - "+endLoadTime+"]");

					//完全按照规则去抽
					Map<String, String> commParam = new HashMap<String, String>();
					commParam.put("schema", schema);
					commParam.put("currTime", currTime);
					commParam.put("endLoadTime", DateUtil.getCurrentDateStr());
				//	commParam.put("hasDynamicTime", dynamicTimeRange != null ? "1":"0");
					//名单抽取
					int size = extractThirdPartyOriginalData(sql1, row, commParam, query ,logParam, taskLog);

					//抽取数量
					taskLog.setExtractNum(size);
					long duration = 0;
					try {
						sw.split();//获取耗时
						duration = sw.getSplitTime();
					}catch (Exception e) {

					}
					taskLog.setDuration(duration);

					//获取接收到智能质检结果的数量
					if(CommonUtil.isNotBlank(taskId)) {
						String taskResultNumKey = "TASK_CALLBACK_NUM_" + taskId;
						Integer taskResultNum = cache.get(taskResultNumKey);
						if(taskResultNum != null) {
							taskLog.setZnResultNum(taskResultNum);
							cache.delete(taskResultNumKey);
						}
					}

					//存储日志信息
					saveTaskLogRecord(schema, taskLogKey, taskLog, logParam);

					//添加追踪日志 --流程内记录CONTENT追踪内容
					logParam.put("DURATION",duration +" ms");

					LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId,TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam);

					logParam.put("CREATE_TIME", DateUtil.addSecond(DateUtil.TIME_FORMAT,EasyCalendar.newInstance().getDateTime("-"),5));
					LogUtils.addTaskTrackLogAutoTypeAfterSecond(user,schema,entId,busiOrderId,TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
							,"第三方质检任务抽取结束,共耗时 "+duration+" ms...",3);
				}

				//更新所有任务的上次执行时间
				if(CommonUtil.listIsNotNull(taskIds)) {
					query.execute("UPDATE " + schema + ".cc_qc_task set PREV_DATE=? "
									+ " WHERE ID in (" + taskIdSb.toString()+  ")"
							, new Object[]{DateUtil.getCurrentDateStr()});
				}
			}else{
				LogUtils.addTaskTrackLogAutoType(user,schema,entId,SchemaUtil.getCcBusiOrderIdByEntId(entId), TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,new JSONObject()
						,"未查到任何在执行时间段内的第三方任务,不执行任何操作.","未查到任何在执行时间段内的第三方任务,不执行任何操作.");
				if(ServerContext.isDebug()) {
					logger.info(" 未查到任何在执行时间段内的第三方任务,不执行任何操作.");
				}
				String updateTaskSql = "UPDATE "+schema+".CC_QC_TASK SET STATE = '3' WHERE START_TIME<=? AND STATE!='3' AND END_TIME IS NOT NULL AND END_TIME<=?";
				query.execute(updateTaskSql, currTime,currTime);

				if(ServerContext.isDebug()) {
					logger.info(" 找出状态不为已关闭且实际已过期的任务,将其状态设置为已关闭! sql >> " + updateTaskSql + ", param >> [" + currTime + ", " + currTime + "]");
				}
			}
		} catch (Exception e) {
			logger.error("执行邮件质检任务失败，原因："+e.getMessage(),e);
		}
		return null;
	}
	/**
	 * 执行邮件任务抽取
	 * 1、查询正在启动中，且未过期的任务
	 * 2、根据任务抽取数据，写入抽取对象、抽取日志
	 * @param schema
	 * @param runTaskId 任务ID
	 * @return
	 */
	public static JSONObject getQcTaskListByEmail(UserModel user,String schema, String entId, String runTaskId) {
		String currTime = DateUtil.getCurrentDateStr();
		EasySQL sql = new EasySQL("SELECT t1.*, t2.EXECUTE_SQL,T2.ONLY_VALID FROM " + schema + ".CC_QC_TASK t1 ");
		sql.append(" LEFT JOIN " + schema + ".CC_QC_EXTRACT t2 ON t1.EXTRACT_ID=t2.ID ");
		sql.append(" WHERE 1=1 ");
		sql.append(runTaskId, " AND t1.ID=? ");//任务
		sql.append("1", " AND t1.STATE=? ");//任务状态 0:待启动 1:启动中 2:已暂停 3:已关闭
		sql.append(" AND t1.IS_PARENT='N' ");//不是父任务
		sql.append(ChannelTypeEnum.EMAIL.getCode(), " AND t1.CHANNEL_TYPE=? ");//渠道为邮件
		sql.append(entId, " AND t1.ENT_ID=? ");
		sql.append(currTime, " AND t1.START_TIME <= ? ");
		sql.append(currTime, " AND (t1.END_TIME >=? OR t1.END_TIME is null)");
		EasyQuery query = QueryFactory.getWriteQuery();
		try {
			if(ServerContext.isDebug()) {
				logger.info(" 查询正在启动中，且未过期的邮件质检任务：sql:" +
						sql.getSQL() + "param:" + JSON.toJSONString(sql.getParams()));
			}
			List<EasyRow>  rows = query.queryForList(sql.getSQL(), sql.getParams());
			if(CommonUtil.listIsNotNull(rows)) {
				List<String> taskIds = new ArrayList<String>();
				StringBuffer taskIdSb = new StringBuffer();
				//计时器
				StopWatch sw = new StopWatch();
				StringBuffer t = new StringBuffer(DateUtil.getCurrentDateStr("YYYYMMddHHmm"));
				t.setCharAt(t.length() - 1, '0');
				String cachePrefix = t.toString() + "_task_log_id_";
				//对每一个任务执行数据抽取:每一个任务的抽取规则不同
				for (EasyRow row : rows) {
					JSONObject logParam = row.toJSONObject();
					String taskId = row.getColumnValue("ID");
					String taskName = row.getColumnValue("TASK_NAME");
					String channelType = row.getColumnValue("CHANNEL_TYPE");
					String executeSql = row.getColumnValue("EXECUTE_SQL");
					entId = row.getColumnValue("ENT_ID");
					String busiOrderId = row.getColumnValue("BUSI_ORDER_ID");
					String extractId = row.getColumnValue("EXTRACT_ID");
					String extractOtherData = row.getColumnValue("EXTRACT_OTHER_DATA");

					//记录邮件渠道任务执行日志
					QcTaskLog taskLog = new QcTaskLog();
					taskLog.setTaskId(taskId);
					taskLog.setExecTime(currTime);
					taskLog.setCreateTime(currTime);
					taskLog.setExecResult(QcTaskLog.EXEC_SUCC);
					taskLog.setEntId(entId);
					taskLog.setBusiOrderId(busiOrderId);

					//任务执行ID
					String taskLogKey = cachePrefix + taskId;
					String taskLogId = cache.get(taskLogKey);
					taskLogId = StringUtils.isBlank(taskLogId) ? RandomKit.randomStr() : taskLogId;
					logParam.put("TASK_EXEC_ID",taskLogId);

					LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
							,"邮件质检任务开始执行抽取...","邮件质检任务开始执行抽取...");

					//任务上次抽取数据时间
					String loadDate = row.getColumnValue("LOAD_DATE");
					//当断当前时间是否在当前任务配置的可运行时间内
					if(!TaskHandler.nowInRunTime(taskId, entId, busiOrderId)){
						LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
								,"质检任务不在可运行时间内,跳过定时抽取！","质检任务不在可运行时间内，跳过定时抽取,请检查任务运行时间配置task="+taskId);
						logger.warn( "任务不在可运行时间内,请检查配置task=" + taskId);

						taskLog.setExecResult(QcTaskLog.EXEC_FAIL);
						taskLog.appendExecDesc("当前时间,已配置成不允许执行,无法执行抽取,请检查质检参数配置.");
						//存储日志信息
						saveTaskLogRecord(schema, taskLogKey, taskLog, logParam);
						continue;
					}
					sw.reset();
					sw.start();
					logger.info("开始执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],上次抽取的数据时间点:"+loadDate);
					taskLog.appendExecDesc("任务抽取开始,上次抽取时间点为:"+loadDate);

					if(CommonUtil.isNotBlank(taskId)) {
						taskIds.add(taskId);
					      if (taskIdSb.length()!=0) {
					       taskIdSb.append(",");
					      }
				      taskIdSb.append("'").append(taskId).append("'");
				     }
					EasySQL exeSql = QueryUtil.getExeSql(schema, entId, busiOrderId, extractId, "T1.",null);
					executeSql = exeSql.getSQL();
					//如果不是邮件的渠道类型或者抽取sql是空，则跳过
					if((!String.valueOf(ChannelTypeEnum.EMAIL.getCode()).equals(channelType)) || StringUtils.isBlank(executeSql)) {
						LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
								,"质检任务抽取邮件Sql为空,跳过定时抽取！","质检任务抽取邮件Sql为空,跳过定时抽取,请检查抽检规则配置task="+taskId);
						logger.error(" 执行数据抽取失败,任务名称["+taskName+"],任务ID["+taskId+"],原因:不是邮件任务 或 抽取邮件sql为空!" );
						continue;
					}
					// 用于拼接查找条件，需要用在需要用在抽取数据可统计坐席数据两个地方，这里先不拼查找的字段，实际使用的时候再拼接
					EasySQL sql1 = new EasySQL(" FROM "  + CommonUtil.getTableName(schema, "C_EMAIL_SESSION T1"));
					sql1.append(" left join CC_USER T11 on T1.HANDLE_ACC = T11.USER_ACCT AND T1.ENT_ID = T11.ENT_ID");
					boolean hasOutSql = QueryUtil.addOutQcSql(schema, entId, busiOrderId, extractId, query, sql1);
					if(!hasOutSql) {
						sql1.append("where 1=1 ");
					}
					sql1.append(EmailEnum.SessionStatusEnum.CLOSED.getCode()," and T1.STATUS = ? "); //只抽取已关闭的会话进行质检
					sql1.append(entId, " and T1.ENT_ID=? ");
					sql1.append(busiOrderId, " and T1.BUSI_ORDER_ID=? ");
					//本次抽取会话记录的默认截止时间，10分钟之前，即默认只抽10分钟之前的记录
					String tempDate = DateUtil.addMinute(DateUtil.TIME_FORMAT, currTime, -10);
					String startLoadTime = "";
					String endLoadTime = tempDate;

					//获取动态时间规则的时间范围
					JSONObject dynamicTimeRange = TaskHandler.getDynamicTimeRange(extractId, schema, query);
					logger.info("dynamicTimeRange:"+dynamicTimeRange);
					if(dynamicTimeRange != null) {
						//存在动态时间 TODO 根据会话关闭日期抽取
						startLoadTime = dynamicTimeRange.getString("startLoadTime");
						endLoadTime = dynamicTimeRange.getString("endLoadTime");
						sql1.append(endLoadTime, " AND T1.CLOSE_TIME <=? ");
						sql1.append(startLoadTime, " AND T1.CLOSE_TIME >=? ");
					} else if(StringUtils.isBlank(loadDate)) {
						//该任务之前没有执行过，上次执行时间为空,此时抽取 会话记录表里最早一条记录+1个月内的数据
						startLoadTime = TaskHandler.getTaskMinTimeForEmail(extractId, entId, busiOrderId, schema, query);
						if(CommonUtil.isNotBlank(startLoadTime)) {
							String lastMoth = DateUtil.addMonth(DateUtil.TIME_FORMAT, startLoadTime, 1);
							if(StringUtils.compareIgnoreCase(tempDate, lastMoth) > 0) {
								endLoadTime = lastMoth;
							}
						}
						//如果不存在上次请求时间，首次请求，挂机时间小于当前质检任务开始时间
						sql1.append(endLoadTime, " AND T1.CLOSE_TIME <? ");
						sql1.append(startLoadTime, " AND T1.CLOSE_TIME >? ");
					}else {
						startLoadTime = loadDate;
						String lastMoth = DateUtil.addMonth(DateUtil.TIME_FORMAT, startLoadTime, 1);
						if(StringUtils.compareIgnoreCase(tempDate, lastMoth) > 0) {
							endLoadTime = lastMoth;
						}
						//如果存在上次请求时间，为不重复请求数据，挂机时间小于当前时间，还需要大于上次请求时间
						sql1.append(endLoadTime, " AND T1.CLOSE_TIME <? ");
						sql1.append(startLoadTime, " AND T1.CLOSE_TIME >? ");
					}
					//记录抽取开始时间
					taskLog.setExtractBeginTime(startLoadTime);

					//是否抽取其他任务内数据
					if (StringUtils.equals("N", extractOtherData)) {
						sql1.append("AND (T1.QC_STATE IS NULL OR T1.QC_STATE = 0)");
						taskLog.appendExecDesc("本次仅仅抽取未被其他任务抽取过的邮件记录.");
					}
					
					logger.info("执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],本次抽取数据范围:["+startLoadTime+" - "+endLoadTime+"]");
					logParam.put("CONTENT","执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],本次抽取数据范围:["+startLoadTime+" - "+endLoadTime+"]");
					//对于邮件，如果任务里指定了结束原因，则按指定的结束原因抽取，如果未指定结束原因，则不指定该条件。
					SQLUtil.addExecuteSql(sql1, exeSql);
					// 查找坐席原始数据抽取率(为空不限制)
					int size = 0;
					Map<String, String> commParam = new HashMap<String, String>();
					commParam.put("schema", schema);
					commParam.put("currTime", currTime);
					commParam.put("endLoadTime", endLoadTime);
					commParam.put("hasDynamicTime", dynamicTimeRange != null ? "1":"0");
					//名单抽取
					size = extractEmailOriginalData(sql1, row, commParam, query ,logParam, taskLog);
					//抽取数量
					taskLog.setExtractNum(size);
					long duration = 0;
					try {
						sw.split();//获取耗时
						duration = sw.getSplitTime();
					}catch (Exception e) {

					}
					taskLog.setDuration(duration);
					//获取接收到智能质检结果的数量
					if(CommonUtil.isNotBlank(taskId)) {
						String taskResultNumKey = "TASK_CALLBACK_NUM_" + taskId;
						Integer taskResultNum = cache.get(taskResultNumKey);
						if(taskResultNum != null) {
							taskLog.setZnResultNum(taskResultNum);
							cache.delete(taskResultNumKey);
						}
					}

					//存储日志信息
					saveTaskLogRecord(schema, taskLogKey, taskLog, logParam);

					//添加追踪日志 --流程内记录CONTENT追踪内容
					logParam.put("DURATION",duration +" ms");

					LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId,TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam);

					logParam.put("CREATE_TIME", DateUtil.addSecond(DateUtil.TIME_FORMAT,EasyCalendar.newInstance().getDateTime("-"),5));
					LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId,TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
							,"邮件质检任务抽取结束,共耗时 "+duration+" ms...","邮件质检任务抽取结束,共耗时 "+duration+" ms...");
				}

				//更新所有任务的上次执行时间
				if(CommonUtil.listIsNotNull(taskIds)) {
					query.execute("UPDATE " + schema + ".cc_qc_task set PREV_DATE=? "
					         + " WHERE ID in (" + taskIdSb.toString()+  ")"
					       , new Object[]{DateUtil.getCurrentDateStr()});
				}
			}else{
				LogUtils.addTaskTrackLogAutoType(user,schema,entId,SchemaUtil.getCcBusiOrderIdByEntId(entId), TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,new JSONObject()
						,"未查到任何在执行时间段内的邮件任务,不执行任何操作.","未查到任何在执行时间段内的邮件任务,不执行任何操作.");
				if(ServerContext.isDebug()) {
					logger.info(" 未查到任何在执行时间段内的邮件任务,不执行任何操作.");
				}
				String updateTaskSql = "UPDATE "+schema+".CC_QC_TASK SET STATE = '3' WHERE START_TIME<=? AND STATE!='3' AND END_TIME IS NOT NULL AND END_TIME<=?";
				query.execute(updateTaskSql, currTime,currTime);

				if(ServerContext.isDebug()) {
					logger.info(" 找出状态不为已关闭且实际已过期的任务,将其状态设置为已关闭! sql >> " + updateTaskSql + ", param >> [" + currTime + ", " + currTime + "]");
				}
			}
		} catch (Exception e) {
			logger.error( "执行邮件质检任务失败，原因："+e.getMessage(),e);
		}
		return null;
	}
	
	
	private static int extractMediaOriginalData(EasySQL extract, EasyRow task, Map<String, String> commParam, EasyQuery query ,JSONObject logParam,QcTaskLog taskLog) throws Exception {
		String taskId = task.getColumnValue("ID");
		String taskName = task.getColumnValue("TASK_NAME");
		String hasDynamicTime = commParam.get("hasDynamicTime");
		String entId = task.getColumnValue("ENT_ID");
		String busiOrderId = task.getColumnValue("BUSI_ORDER_ID");
		String schema = commParam.get("schema");
		//动态时间时为了防止一个任务重复质检相同的数据，限制每条记录只质检一次
		if(Constants.isOpenQcOnlyOne() ) {
			//如果开启每条记录只质检一次,只取未质检状态的数据
			extract.append("0", "AND T1.QC_STATE=?");
			taskLog.appendExecDesc("由于质检参数配置里开启了一条记录只运行抽取一次,本次将不再抽取已被抽取过的全媒体.");
		}
		StringBuffer selecterSql = new StringBuffer("SELECT T1.SERIAL_ID,T1.BEGIN_TIME,T1.END_TIME,T1.AGENT_ID,T1.cust_name,T1.SESSION_SEQ,T1.SATISF_NAME ");
		selecterSql.append(extract.getSQL());
		selecterSql.append(" order by T1.END_TIME ");
		if(ServerContext.isDebug()) {
			logger.info(" 执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],抽取全媒体数据 sql:" + 
					selecterSql.toString() + "param:" + JSON.toJSONString(extract.getParams()));
		}
		List<JSONObject>  serialList = query.queryForList(selecterSql.toString(), extract.getParams(), new JSONMapperImpl());

		taskLog.appendExecDesc("本次执行抽取到的数量:"+( serialList==null ? 0 :serialList.size()) );

		logParam.put("EXEC_SQL",selecterSql.toString() + ";param:" + JSON.toJSONString(extract.getParams()));
		LogUtils.addTaskTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
				,">>>执行全媒体数据抽取...","");
		return addMediaTaskObj(serialList, task, commParam, false, null, query ,logParam,taskLog);
	}

	public static int addMediaTaskObj(List<JSONObject>  serialList, EasyRow task, Map<String, String> commParam, boolean isLimit
			, Map<String, Integer> angetNumMap, EasyQuery query ,JSONObject logParam,QcTaskLog taskLog) throws Exception {
		//抽取成功的总量
		int size = 0;
		//找不到坐席信息的总量
		int noAgentSize = 0;
		//找不到id的总量
		int noIdSize = 0;
		//非质检组里的坐席数据总量
		int noQcGroupAgent = 0;
		//已被该任务抽取过的总量
		int alreadyQcNum = 0;
		//已被该任务的父任务抽取过的总量
		int alreadyQcByParentNum = 0;

		String taskId = task.getColumnValue("ID");
		String taskName = task.getColumnValue("TASK_NAME");
		String channelType = task.getColumnValue("CHANNEL_TYPE");
		String entId = task.getColumnValue("ENT_ID");
		String busiOrderId = task.getColumnValue("BUSI_ORDER_ID");
		String examGroupId = task.getColumnValue("EXAM_GROUP_ID");
		String schema = commParam.get("schema");
		String currTime = commParam.get("currTime");
		String endLoadTime = commParam.get("endLoadTime");
		String maxEndTime = "";
		Set<String> setIds = new HashSet<String>();
		List<JSONObject> extObjectList = new ArrayList<>();
		Map<String ,Integer> errMap = new HashMap<>();
		//查询对应该质检任务对应的质检组是否有质检对象，没有则默认按抽取全部人员
		boolean isExtractAll = isExtractAllObj(query, schema, examGroupId);
		if(CommonUtil.listIsNotNull(serialList)) {
			//查询该任务对应的质检组里的所有坐席，该任务里没有设置坐席时，不执行任务抽取
			Set<String> angetAccSet = null;
			if(!isLimit) {
				angetAccSet = getTaskAgnets(examGroupId, schema, entId, busiOrderId, query, "AGENT_ACC");
			}else {
				angetAccSet = angetNumMap.keySet();
			}
			for (JSONObject easyRow : serialList) {
				String serialId = easyRow.getString("SERIAL_ID");
				String endTime = easyRow.getString("END_TIME");
				String beginTime = easyRow.getString("BEGIN_TIME");
				//全媒体AGENT_ID存的是账号
				String agentId = easyRow.getString("AGENT_ID");
				String satisfName = easyRow.getString("SATISF_NAME");
				JSONObject agent = CacheUtil.getCcUserCache().getCache(entId, busiOrderId, agentId);
				JSONObject taskObjLog = LogUtils.getTaskObjTrackLogParam(task, easyRow ,agent ,logParam);
				String agentInfo = agent == null ? "" : agent.getString("USERNAME")+"("+agent.getString("USER_ACCT")+")";
				if(StringUtils.compareIgnoreCase(endTime, maxEndTime) > 0) {
					maxEndTime = endTime;
				}
				
				if(agent == null) {
					noAgentSize ++;
					errMap.put(ErrorMsgEnum.IS_OVER.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.IS_OVER.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:找不到坐席【"+agentInfo+"】数据;ENT_ID=" + entId +", BUSI_ORDER_ID=" + busiOrderId + ", AGENT_ID=" + agentId);
					LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					logger.info("找不到坐席数据entId=" + entId +", busiOrderId=" + busiOrderId + ", agentId=" + agentId);
					
					continue;
				}
				if(StringUtils.isBlank(serialId) || setIds.contains(serialId)) {
					noIdSize ++;
					errMap.put(ErrorMsgEnum.DATA_EXCEP.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.DATA_EXCEP.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:该全媒体记录数据异常;SERIAL_ID=" + serialId);
					logger.info("抽取失败:该全媒体记录数据异常;SERIAL_ID=" + serialId);
					LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					continue;
				}

				if (!isExtractAll){
					//如果质检组不是空，并且坐席不存在质检组里，不质检这条数据
					if(angetAccSet.size() > 0 && !angetAccSet.contains(agentId)) {
						noQcGroupAgent ++;
						errMap.put(ErrorMsgEnum.AGENT_NO_IN_GROUP.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.AGENT_NO_IN_GROUP.getCode())+"")+1);
						taskObjLog.put("CONTENT","抽取失败:坐席【"+agentInfo+"】不存在质检组里,不质检这条数据。");
						logger.info("抽取失败:坐席【"+agentInfo+"】不存在质检组里,不质检这条数据。");
						LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
						continue;
					}
				}

				//判断是否存在聊天记录,如果不存在聊天记录则不质检
				if(!QueryUtil.isExistChatMsg(serialId, schema, query)){
					alreadyQcNum ++ ;
					errMap.put(ErrorMsgEnum.NOT_CHAT_HIS.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.NOT_CHAT_HIS.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:该全媒体记录不存在聊天记录,不进行质检。");
					logger.info("抽取失败:该全媒体记录不存在聊天记录,不进行质检。");
					LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					continue;
				}
				
				//判断聊天记录是否抽取过，如果已经存在质检记录则不再处理
				if(existQcObj(taskId, serialId, schema, query)){
					alreadyQcByParentNum ++ ;
					errMap.put(ErrorMsgEnum.IS_CHECKED.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.IS_CHECKED.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:该全媒体记录已被抽取过。");
					logger.info("抽取失败:该全媒体记录已被抽取过。");
					//LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					continue;
				}
				//判断话单是否被父任务抽取过
			    if(existQcParentObj(taskId, serialId, schema, query,task.getColumnValue("PARENT_ID")).size()>0) {
			     errMap.put(ErrorMsgEnum.IS_CHECKED.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.IS_CHECKED.getCode())+"")+1);
			     taskObjLog.put("CONTENT","抽取失败:该全媒体记录已被父任务抽取过。");
			     logger.info("抽取失败:该全媒体记录已被父任务抽取过。");
			     //LogUtils.addTaskObjTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
			     continue;
			    }
				setIds.add(serialId);
				EasyRecord er = new EasyRecord(schema + ".CC_QC_TASK_OBJ", "ID");
				er.set("ID", RandomKit.randomStr());
				er.set("SERIAL_ID", serialId);
				er.set("ENT_ID", entId);
				er.set("BUSI_ORDER_ID", busiOrderId);
				er.set("CREATE_TIME", currTime);
				er.set("UPDATE_TIME", currTime);
				er.set("TASK_ID", taskId);
				er.set("CHANNEL_TYPE", channelType == null ? "" : channelType);
				er.set("ZN_STATE", Constants.QC_STATE_EXTRACT);
				er.set("RG_STATE", Constants.QC_STATE_EXTRACT);
				//服务时间,通话或会话开始时间,格式如2020-12-12 12:00:00
				er.set("SERVICE_TIME", beginTime);
				er.set("SATISF_NAME", satisfName);
				er.set("OBJ_STATE", Constants.QC_GLOBAL_STATE_ZN);
				er.set("DATE_ID", CommonUtil.parseInteger(currTime.substring(0,10).replaceAll("-", "")));
				//20220421
				if(agent !=null){
					er.set("AGENT_ID", agent.getString("USER_ID"));
					er.set("AGENT_ACC", agent.getString("USER_ACCT"));
					er.set("AGENT_NAME", agent.getString("USERNAME"));
					er.set("AGENT_DEPT_CODE", agent.getString("DEPT_CODE"));
				}
				query.save(er);
				
				//将全媒体记录改为已质检状态
				EasyRecord updateMedia = new EasyRecord(schema + ".CC_MEDIA_RECORD", "SERIAL_ID");
				updateMedia.setPrimaryValues(serialId);
				updateMedia.set("QC_STATE", Constants.QC_STATE_EXTRACT);
				query.update(updateMedia);
				
				size += 1;
				taskObjLog.put("CONTENT","抽取成功:该全媒体记录被抽取到【"+taskName+"】当中。");
				LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
				extObjectList.add(easyRow);
			}
			if(size == 0) {
				maxEndTime = endLoadTime;
			}
			QueryUtil.uodateQcTaskLoadTime(schema, taskId, maxEndTime, query);
			taskLog.setExtractEndTime(maxEndTime);

			logParam.put("CONTENT",logParam.getString("CONTENT")+">>>质检任务抽取成功,过滤不符合条件的数据后,共抽取["+size+"条]数据");

			taskLog.appendExecDesc("本次质检任务抽取后剔除的数据总量:"+(noAgentSize+noIdSize+noQcGroupAgent+alreadyQcNum+alreadyQcByParentNum)
					+",包含: 记录找不到坐席的总量:"+noAgentSize
					+",记录无ID的总量:"+noIdSize
					+",非质检组里的坐席数据总量:"+noQcGroupAgent
					+",已被该任务抽取过的总量:"+alreadyQcNum
					+",已被该任务的父任务抽取过的总量:"+alreadyQcByParentNum
					+"<br>"
			);
		}else {
			//抽取不到数据时更新数据加载时间到endLoadTime
			QueryUtil.uodateQcTaskLoadTime(schema, taskId, endLoadTime, query);
			taskLog.setExtractEndTime(endLoadTime);
			logParam.put("CONTENT",">>>质检任务抽取完成,该抽取范围内没有符合条件的数据。");
		}
		taskLog.appendExecDesc("本次抽取后最终总量:"+size);

		LogUtils.addErrMsgLog(logParam, entId, busiOrderId, schema, errMap);
		return size;
	}

	/**
	 *
	 * @param extract
	 * @param task
	 * @param commParam
	 * @param query
	 * @param logParam
	 * @return
	 */
	private static int extractThirdPartyOriginalData(EasySQL extract, EasyRow task
			, Map<String, String> commParam, EasyQuery query , JSONObject logParam,QcTaskLog taskLog) throws Exception {
		String taskId = task.getColumnValue("ID");
		String taskName = task.getColumnValue("TASK_NAME");
		String hasDynamicTime = commParam.get("hasDynamicTime");
		String entId = task.getColumnValue("ENT_ID");
		String busiOrderId = task.getColumnValue("BUSI_ORDER_ID");
		String schema = commParam.get("schema");
		//动态时间时为了防止一个任务重复质检相同的数据，限制每条记录只质检一次
		if(Constants.isOpenQcOnlyOne()) {
			//如果开启每条记录只质检一次,只取未质检状态的数据（质检状态 1-待质检  2-已质检）
			extract.append("1", "AND T1.STATUS=?");
			taskLog.appendExecDesc("由于质检参数配置里开启了一条记录只运行抽取一次,本次将不再抽取已被抽取过的第三方记录.");
		}

		//全部字段吧
		StringBuffer selecterSql = new StringBuffer("SELECT T1.ID AS SERIAL_ID,T1.CREATE_TIME,T1.AGENT_ACC AS AGENT_ID ");
		selecterSql.append(extract.getSQL());
		selecterSql.append(" order by T1.CREATE_TIME ");
		if(ServerContext.isDebug()) {
			logger.info(" 执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],抽取第三方数据 sql:" +
					selecterSql + "param:" + JSON.toJSONString(extract.getParams()));
		}
		List<JSONObject>  serialList = query.queryForList(selecterSql.toString(), extract.getParams(), new JSONMapperImpl());

		taskLog.appendExecDesc("本次执行抽取到的数量:"+( serialList==null ? 0 :serialList.size()) );

		logParam.put("EXEC_SQL",selecterSql + ";param:" + JSON.toJSONString(extract.getParams()));
		LogUtils.addTaskTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
				,">>>执行第三方数据抽取...","");
		return addThirdPartyTaskObj(serialList, task, commParam, false, null, query ,logParam,taskLog);
	}
	
	
	/**
	 * 抽取工单原始数据
	 * @param extract
	 * @param task
	 * @param commParam
	 * @param query
	 * @return
	 */
	private static int extractOrderOriginalData(EasySQL extract, EasyRow task, Map<String, String> commParam, EasyQuery query ,JSONObject logParam,QcTaskLog taskLog) throws Exception {
		String taskId = task.getColumnValue("ID");
		String taskName = task.getColumnValue("TASK_NAME");
		String hasDynamicTime = commParam.get("hasDynamicTime");
		String entId = task.getColumnValue("ENT_ID");
		String busiOrderId = task.getColumnValue("BUSI_ORDER_ID");
		String schema = commParam.get("schema");
		if(Constants.isOpenQcOnlyOne() ) {
			//如果开启每条记录只质检一次,只取未质检状态的数据
			extract.append(" AND C_BO_BASE_ORDER.QC_STATE='0' ");
			taskLog.appendExecDesc("由于质检参数配置里开启了一条记录只运行抽取一次,本次将不再抽取已被抽取过的工单.");
		}
		String hisSql = extract.getSQL().replaceAll("C_BO_BASE_ORDER", "C_BO_BASE_ORDER_HIS");
		
		List<Object> list = Arrays.asList(extract.getParams());
		ArrayList<Object> arrayList = new ArrayList<>(list);
		
		List<Object> list2 = Arrays.asList(extract.getParams());
		ArrayList<Object> arrayList2 = new ArrayList<>(list2);
		arrayList.addAll(arrayList2);
		
		StringBuffer selecterSql = new StringBuffer("SELECT C_BO_BASE_ORDER.* ");
		selecterSql.append(" " + extract.getSQL());
		selecterSql.append(" union all"  );
		selecterSql.append(" SELECT C_BO_BASE_ORDER_HIS.* "  );
		selecterSql.append(" " + hisSql);
		selecterSql.append(" ORDER BY  END_TIME ");
		
		if(ServerContext.isDebug()) {
			logger.info(" 执行工单数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],抽取工单数据 sql:" + 
					selecterSql.toString() + "param:" + JSON.toJSONString(arrayList));
		}
//		taskLog.appendExecDesc("本次执行抽取的SQL:"+selecterSql.toString());
//		taskLog.appendExecDesc("本次执行抽取的SQL条件:"+JsonUtil.toJSONString(arrayList));
		
		//查找抽取数据
		List<JSONObject>  serialIds = query.queryForList(selecterSql.toString(), arrayList.toArray(), new JSONMapperImpl());
		
		taskLog.appendExecDesc("本次执行抽取到的数量:"+( serialIds==null ? 0 :serialIds.size()) );

		//添加追踪日志
		logParam.put("EXEC_SQL",selecterSql.toString() + ";param:" + JSON.toJSONString(extract.getParams()));
		LogUtils.addTaskTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
				,">>>执行工单数据抽取...","");
		return addOrderTaskObj(serialIds, task, commParam, false, null, query ,logParam,taskLog);
	}

	public static int addThirdPartyTaskObj(List<JSONObject>  serialList, EasyRow task
			, Map<String, String> commParam, boolean isLimit
			, Map<String, Integer> angetNumMap, EasyQuery query ,JSONObject logParam,QcTaskLog taskLog) throws Exception {
		//抽取成功的总量
		int size = 0;
		//找不到坐席信息的总量
		int noAgentSize = 0;
		//找不到id的总量
		int noIdSize = 0;
		//非质检组里的坐席数据总量
		int noQcGroupAgent = 0;
		//已被该任务抽取过的总量
		int alreadyQcNum = 0;
		//已被该任务的父任务抽取过的总量
		int alreadyQcByParentNum = 0;

		String taskId = task.getColumnValue("ID");
		String taskName = task.getColumnValue("TASK_NAME");
		String channelType = task.getColumnValue("CHANNEL_TYPE");
		String entId = task.getColumnValue("ENT_ID");
		String busiOrderId = task.getColumnValue("BUSI_ORDER_ID");
		String examGroupId = task.getColumnValue("EXAM_GROUP_ID");
		String schema = commParam.get("schema");
		String currTime = commParam.get("currTime");
		String endLoadTime = commParam.get("endLoadTime");
		String templateId = task.getColumnValue("TEMPLATE_ID");
		String maxEndTime = "";
		Set<String> setIds = new HashSet<String>();
		List<JSONObject> extObjectList = new ArrayList<>();
		Map<String ,Integer> errMap = new HashMap<>();
		//第三方质检记录并一定有传递对应的agentId，默认不根据配置的质检对象进行过滤
		if(CommonUtil.listIsNotNull(serialList)) {
			//查询该任务对应的质检组里的所有坐席，该任务里没有设置坐席时，不执行任务抽取
			Set<String> angetAccSet = null;
			if(!isLimit) {
				angetAccSet = getTaskAgnets(examGroupId, schema, entId, busiOrderId, query, "AGENT_ACC");
			}else {
				angetAccSet = angetNumMap.keySet();
			}
			for (JSONObject easyRow : serialList) {
				String serialId = easyRow.getString("SERIAL_ID");
				String createTime = easyRow.getString("CREATE_TIME");
				//String beginTime = easyRow.getString("BEGIN_TIME");
				//本质上是AGENT_ACC 外层SQL:T1.AGENT_ACC AS AGENT_ID
				String agentId = easyRow.getString("AGENT_ID");
				JSONObject agent = CacheUtil.getCcUserCache().getCache(entId, busiOrderId, agentId);
				JSONObject taskObjLog = LogUtils.getTaskObjTrackLogParam(task, easyRow ,agent ,logParam);
				//查询对应该质检任务对应的质检组是否有质检对象，没有则默认按抽取全部人员
				boolean isExtractAll = isExtractAllObj(query, schema, examGroupId);
				String agentInfo = agent == null ? "" : agent.getString("USERNAME")+"("+agent.getString("USER_ACCT")+")";
				
				if (agent==null) {
					agent = new JSONObject();
				}
				
				if(StringUtils.compareIgnoreCase(createTime, maxEndTime) > 0) {
					maxEndTime = createTime;
				}

				/**if(agent == null) {
					errMap.put(ErrorMsgEnum.NOT_AGENT.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.NOT_AGENT.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:找不到坐席【"+agentInfo+"】数据;ENT_ID=" + entId +", BUSI_ORDER_ID=" + busiOrderId + ", AGENT_ID=" + agentId);
					LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					logger.error(CommonUtil.getClassNameAndMethod(QcTaskServiceHandler.class) + "找不到坐席数据entId=" + entId +", busiOrderId=" + busiOrderId + ", agentId=" + agentId);
					continue;
				}**/
				if(StringUtils.isBlank(serialId) || setIds.contains(serialId)) {
					noIdSize ++;
					errMap.put(ErrorMsgEnum.DATA_EXCEP.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.DATA_EXCEP.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:该第三方记录数据异常;SERIAL_ID=" + serialId);
					LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					continue;
				}

				if (!isExtractAll){
					//如果质检组不是空，并且坐席不存在质检组里，不质检这条数据
					if(angetAccSet.size() > 0 && !angetAccSet.contains(agentId)) {
						noQcGroupAgent ++;
						errMap.put(ErrorMsgEnum.AGENT_NO_IN_GROUP.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.AGENT_NO_IN_GROUP.getCode())+"")+1);
						taskObjLog.put("CONTENT","抽取失败:坐席【"+agentId+"】不存在质检组里,不质检这条第三方记录。");
						logger.info("抽取失败:坐席【"+agentId+"】不存在质检组里,不质检这条数据。");
						LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
						continue;
					}
				}

				//判断第三方是否抽取过，如果已经存在质检记录则不再处理
				if(existQcObj(taskId, serialId, schema, query)){
					alreadyQcNum ++ ;
					errMap.put(ErrorMsgEnum.IS_CHECKED.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.IS_CHECKED.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:该第三方记录已被抽取过。");
					//LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					continue;
				}
				//判断话单是否被父任务抽取过
			    if(existQcParentObj(taskId, serialId, schema, query,task.getColumnValue("PARENT_ID")).size()>0) {
					alreadyQcByParentNum ++ ;
			     	errMap.put(ErrorMsgEnum.IS_CHECKED.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.IS_CHECKED.getCode())+"")+1);
			     	taskObjLog.put("CONTENT","抽取失败:该第三方记录已被父任务抽取过。");
			     	logger.info("抽取失败:该第三方记录已被父任务抽取过。");
			     	//LogUtils.addTaskObjTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
			     	continue;
			    }
				/**
				 //判断抽取坐席原始数据是否已超过限制
				if(isLimit) {
					Integer num = angetNumMap.get(agentId) == null ? 0 : angetNumMap.get(agentId);
					if(num <= 0) {
						errMap.put(ErrorMsgEnum.IS_OVER.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.IS_OVER.getCode())+"")+1);
						taskObjLog.put("CONTENT","抽取失败:坐席【"+agentInfo+"】原始数据抽检已达到上限。");
						LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
						logger.warn(CommonUtil.getClassNameAndMethod(QcTaskServiceHandler.class)+" 坐席原始数据抽检已达到上限,任务名称["+taskName+"],任务ID["+taskId+"],坐席ID[" + agentId + "]");
						continue;
					}else {
						angetNumMap.put(agentId, num - 1);
					}
				}**/

				setIds.add(serialId);
				EasyRecord er = new EasyRecord(schema + ".CC_QC_TASK_OBJ", "ID");
				er.set("ID", RandomKit.randomStr());
				er.set("SERIAL_ID", serialId);
				er.set("ENT_ID", entId);
				er.set("BUSI_ORDER_ID", busiOrderId);
				er.set("CREATE_TIME", currTime);
				er.set("UPDATE_TIME", currTime);
				er.set("TASK_ID", taskId);
				er.set("CHANNEL_TYPE", channelType == null ? "" : channelType);
				er.set("ZN_STATE", Constants.QC_STATE_EXTRACT);
				er.set("RG_STATE", Constants.QC_STATE_EXTRACT);
				//服务时间,通话或会话开始时间,格式如2020-12-12 12:00:00
				er.set("SERVICE_TIME", createTime);
				er.set("DATE_ID",CommonUtil.parseInteger(currTime.substring(0,10).replaceAll("-", "")));
				er.set("AGENT_ID", agent.getString("USER_ID"));
				er.set("AGENT_ACC", agent.getString("USER_ACCT"));
				er.set("AGENT_NAME", agent.getString("USERNAME"));
				er.set("AGENT_DEPT_CODE", agent.getString("DEPT_CODE"));

				//随便存一下第三方质检模板
				er.set("TEMPLATE_ID", templateId);

				query.save(er);

				//将第三方记录改为已质检状态
				EasyRecord updateMedia = new EasyRecord(schema + ".cc_qc_third_record", "ID");
				updateMedia.setPrimaryValues(serialId);
				updateMedia.set("STATUS", "2"); //质检状态 1-待质检  2-已质检
				query.update(updateMedia);

				size += 1;
				taskObjLog.put("CONTENT","抽取成功:该第三方记录被抽取到【"+taskName+"】当中。");
				LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
				extObjectList.add(easyRow);
			}
			if(size == 0) {
				maxEndTime = endLoadTime;
			}
			QueryUtil.uodateQcTaskLoadTime(schema, taskId, maxEndTime, query);
			taskLog.setExtractEndTime(maxEndTime);
			logParam.put("CONTENT",logParam.getString("CONTENT")+">>>质检任务抽取成功,过滤不符合条件的数据后,共抽取["+size+"条]数据");

			taskLog.appendExecDesc("本次质检任务抽取后剔除的数据总量:"+(noAgentSize+noIdSize+noQcGroupAgent+alreadyQcNum+alreadyQcByParentNum)
					+",包含: 记录找不到坐席的总量:"+noAgentSize
					+",记录无ID的总量:"+noIdSize
					+",非质检组里的坐席数据总量:"+noQcGroupAgent
					+",已被该任务抽取过的总量:"+alreadyQcNum
					+",已被该任务的父任务抽取过的总量:"+alreadyQcByParentNum
					+"<br>"
			);
		}else {
			//抽取不到数据时更新数据加载时间到endLoadTime
			QueryUtil.uodateQcTaskLoadTime(schema, taskId, endLoadTime, query);
			taskLog.setExtractEndTime(endLoadTime);
			logParam.put("CONTENT",">>>质检任务抽取完成,该抽取范围内没有符合条件的数据。");
		}

		taskLog.appendExecDesc("本次抽取后最终总量:"+size);
		LogUtils.addErrMsgLog(logParam, entId, busiOrderId, schema, errMap);

		return size;
	}
	
	/**
	 * 执行工单任务抽取
	 * 1、查询正在启动中，且未过期的任务
	 * 2、根据任务抽取数据，写入抽取对象、抽取日志
	 * @param schema
	 * @param runTaskId 任务ID
	 * @return
	 */
	public static JSONObject getQcTaskListByOrder(UserModel user,String schema, String entId, String runTaskId) {
		String currTime = DateUtil.getCurrentDateStr();
		EasySQL sql = new EasySQL("SELECT t1.*, t2.EXECUTE_SQL,T2.ONLY_VALID,T2.APPEAL_SOURCE,T2.CREATE_TYPE FROM " + schema + ".CC_QC_TASK t1 ");
		sql.append(" LEFT JOIN " + schema + ".CC_QC_EXTRACT t2 ON t1.EXTRACT_ID=t2.ID ");
		sql.append(" WHERE 1=1 ");
		sql.append(runTaskId, " AND t1.ID=? ");//任务
		sql.append(" AND t1.IS_PARENT='N' ");//不是父任务
		sql.append("1", " AND t1.STATE=? ");//任务状态 0:待启动 1:启动中 2:已暂停 3:已关闭
		sql.append(ChannelTypeEnum.ORDER.getCode(), " AND t1.CHANNEL_TYPE=? ");//渠道为工单
		sql.append(entId, " AND t1.ENT_ID=? ");
		sql.append(currTime, " AND t1.START_TIME <= ? ");
		sql.append(currTime, " AND (t1.END_TIME >=? OR t1.END_TIME is null)");
		EasyQuery query = QueryFactory.getWriteQuery();
		try {
			if(ServerContext.isDebug()) {
				logger.info(" 查询正在启动中，且未过期的工单质检任务：sql:" +
						sql.getSQL() + "param:" + JSON.toJSONString(sql.getParams()));
			}
			List<EasyRow>  rows = query.queryForList(sql.getSQL(), sql.getParams());
			if(CommonUtil.listIsNotNull(rows)) {
				List<String> taskIds = new ArrayList<String>();
				StringBuffer taskIdSb = new StringBuffer();
				//计时器
				StopWatch sw = new StopWatch();
				StringBuffer t = new StringBuffer(DateUtil.getCurrentDateStr("YYYYMMddHHmm"));
				t.setCharAt(t.length() - 1, '0');
				String cachePrefix = t.toString() + "_task_log_id_";
				//对每一个任务执行数据抽取:每一个任务的抽取规则不同
				for (EasyRow row : rows) {
					JSONObject logParam = row.toJSONObject();
					String taskId = row.getColumnValue("ID");
					String taskName = row.getColumnValue("TASK_NAME");
					String channelType = row.getColumnValue("CHANNEL_TYPE");
					String executeSql = row.getColumnValue("EXECUTE_SQL");
					entId = row.getColumnValue("ENT_ID");
					String busiOrderId = row.getColumnValue("BUSI_ORDER_ID");
					String extractId = row.getColumnValue("EXTRACT_ID");
					// 当作是流程key
					String templateId = row.getColumnValue("TEMPLATE_ID");
					
					String extractOtherData = row.getColumnValue("EXTRACT_OTHER_DATA");
					String appealSource = row.getColumnValue("APPEAL_SOURCE");
					String createType = row.getColumnValue("CREATE_TYPE");

					//执行日志
					QcTaskLog taskLog = new QcTaskLog();
					taskLog.setTaskId(taskId);
					taskLog.setExecTime(currTime);
					taskLog.setCreateTime(currTime);
					taskLog.setExecResult(QcTaskLog.EXEC_SUCC);
					taskLog.setEntId(entId);
					taskLog.setBusiOrderId(busiOrderId);

					//任务执行ID
					String taskLogKey = cachePrefix + taskId;
					String taskLogId = cache.get(taskLogKey);
					taskLogId = StringUtils.isBlank(taskLogId) ? RandomKit.randomStr() : taskLogId;
					logParam.put("TASK_EXEC_ID",taskLogId);

					LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
							,"工单质检任务["+taskId+"]->"+taskName+" 开始执行抽取...","工单质检任务["+taskId+"]->"+taskName+" 开始执行抽取...");

					String startTime = row.getColumnValue("START_TIME");
					String endTime = row.getColumnValue("END_TIME");
					if (!(DateUtil.compareDate(currTime,startTime,DateUtil.TIME_FORMAT) >= 0 && (DateUtil.compareDate(currTime,endTime,DateUtil.TIME_FORMAT) <= 0 || StringUtils.isBlank(endTime)))){
						LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
								,"质检任务不在启动时间内,跳过定时抽取！","质检任务不在启动时间内,跳过定时抽取,请检查任务运行时间配置task="+taskId);
						logger.warn("质检任务不在启动时间内,跳过定时抽取,请检查配置task=" + taskId);
						
						taskLog.setExecResult(QcTaskLog.EXEC_FAIL);
						taskLog.appendExecDesc("当前时间,不在质检任务的开始时间和结束时间内,无法执行抽取.");
						//存储日志信息
						saveTaskLogRecord(schema, taskLogKey, taskLog, logParam);
						continue;
					}

					// 仅抽取有效数据Y是，N否
					String onlyValid = row.getColumnValue("ONLY_VALID");
					//任务上次抽取数据时间
					String loadDate = row.getColumnValue("LOAD_DATE");
					//当断当前时间是否在当前任务配置的可运行时间内
					if(!TaskHandler.nowInRunTime(taskId, entId, busiOrderId)){
						LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
								,"质检任务不在可运行时间内,跳过定时抽取！","质检任务不在可运行时间内，跳过定时抽取,请检查任务运行时间配置task="+taskId);
						logger.warn("任务不在可运行时间内,请检查配置task=" + taskId);
						
						taskLog.setExecResult(QcTaskLog.EXEC_FAIL);
						taskLog.appendExecDesc("当前时间,已配置成不允许执行,无法执行抽取,请检查质检参数配置.");
						//存储日志信息
						saveTaskLogRecord(schema, taskLogKey, taskLog, logParam);
						continue;
					}
					sw.reset();
					sw.start();
					logger.info("开始执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],上次抽取的数据时间点:"+loadDate);
					taskLog.appendExecDesc("任务抽取开始,上次抽取时间点为:"+loadDate);
					
					if (CommonUtil.isNotBlank(taskId)) {
						taskIds.add(taskId);
						if (taskIdSb.length() != 0) {
							taskIdSb.append(",");
						}
						taskIdSb.append("'").append(taskId).append("'");
					}
					
					// 用于拼接查找条件，需要用在需要用在抽取数据可统计坐席数据两个地方，这里先不拼查找的字段，实际使用的时候再拼接
					EasySQL sql1 = new EasySQL(" FROM "  + CommonUtil.getTableName(schema, "C_BO_BASE_ORDER"));
					
					
					JSONObject exeOrderJson = QueryUtil.getExeOrderSql(schema, entId, extractId);
					if (exeOrderJson!=null) {
						EasySQL sqlJson = (EasySQL) exeOrderJson.get("sqlJson");
//						JSONObject sqlJson = exeOrderJson.getJSONObject("sqlJson");
						JSONObject tableJson = exeOrderJson.getJSONObject("tableJson");
						if (sqlJson == null || sqlJson.getParams() == null || sqlJson.getParams().length==0) {
							LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
									,"质检任务抽取工单Sql为空,跳过定时抽取！","质检任务抽取工单Sql为空,跳过定时抽取,请检查抽检规则配置task="+taskId);
							logger.error(" 执行数据抽取失败,任务名称["+taskName+"],任务ID["+taskId+"],原因:不是工单任务 或 抽取工单sql为空!" );
							continue;
						}
						//添加业务表关联
						for (String key:tableJson.keySet()) {
							String tableName = tableJson.getString(key);
							sql1.append("LEFT JOIN "+CommonUtil.getTableName(schema,tableName)+" ON "+tableName+".M_ID = C_BO_BASE_ORDER.ID" );
						}
						if (StringUtils.isNotBlank(appealSource)) {
							if (!sql1.getSQL().contains("C_BOX_MUSIC_STANDARD_BASE")){
								sql1.append("LEFT JOIN "+CommonUtil.getTableName(schema,"C_BOX_MUSIC_STANDARD_BASE")+" ON C_BOX_MUSIC_STANDARD_BASE.M_ID = C_BO_BASE_ORDER.ID" );
							}
							sqlJson.appendIn(appealSource.split(",")," AND C_BOX_MUSIC_STANDARD_BASE.APPEAL_SOURCE ");
						}
						if (StringUtils.isNotBlank(createType)){
							if (!sql1.getSQL().contains("c_box_music_standard_ex")){
								sql1.append("LEFT JOIN "+CommonUtil.getTableName(schema,"c_box_music_standard_ex")+" ON c_box_music_standard_ex.M_ID = C_BO_BASE_ORDER.ID" );
							}
							if ("1".equals(createType)){
								sqlJson.append(" AND c_box_music_standard_ex.HANDLE_AGENT_ACC != 'system'");
							} else if ("2".equals(createType)){
								sqlJson.append(" AND c_box_music_standard_ex.HANDLE_AGENT_ACC = 'system'");
							}
						}


						//工单渠道抽取数据状态更新
						sql1.append(" where 1=1 AND C_BO_BASE_ORDER.STATUS IN (2,3)  ");
						DBTypes types = query.getTypes();
						if(DBTypes.MYSQL == types){
							sql1.append("and C_BO_BASE_ORDER.ORDER_CHARGE <> '' and C_BO_BASE_ORDER.ORDER_CHARGE is not null  ");
						}else if(DBTypes.ORACLE == types|| DBTypes.DAMENG == types){
							sql1.append("and C_BO_BASE_ORDER.ORDER_CHARGE <> '' and C_BO_BASE_ORDER.ORDER_CHARGE is not null  ");
						}else if(DBTypes.PostgreSql == types){
							sql1.append("  and C_BO_BASE_ORDER.ORDER_CHARGE is not null  ");
						}
						
						//控制查询哪个工单流程的数据
						sql1.append(templateId," AND C_BO_BASE_ORDER.PROC_KEY = ? ",false);
						//添加抽取条件
						/*for (String key:sqlJson.keySet()) {
							EasySQL tempSql = (EasySQL) sqlJson.get(key);
							SQLUtil.addExecuteSql(sql1, tempSql);
						}*/
						SQLUtil.addExecuteSql(sql1, sqlJson);
						//判断是否有动态时间 
						boolean booleanValue = exeOrderJson.getBooleanValue("isDynamicTime");
						boolean dateFlag = exeOrderJson.getBooleanValue("dateFlag");
						if (!booleanValue && !dateFlag) {
							//没有时间条件 则按时间升序
							sql1.append(loadDate,"AND C_BO_BASE_ORDER.END_TIME >= ? ");
							//sql1.append(" ORDER BY C_BO_BASE_ORDER.END_TIME");
						}
					}
					//是否抽取其他任务内数据
					if (StringUtils.equals("N", extractOtherData)) {
						sql1.append("AND (C_BO_BASE_ORDER.QC_STATE IS NULL OR C_BO_BASE_ORDER.QC_STATE = 0)");
						taskLog.appendExecDesc("本次仅仅抽取未被其他任务抽取过的工单.");
					}
					
					if (StringUtils.isNotBlank(loadDate)) {
						sql1.append(loadDate,"AND C_BO_BASE_ORDER.END_TIME >= ? ");
						taskLog.setExtractBeginTime(loadDate);
					}
					
					logger.info("执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"] SQL:"+sql1.getSQL()+",param:"+JSON.toJSONString(sql1.getParams()));
					logParam.put("CONTENT","执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"] SQL:"+sql1.getSQL()+",param:"+JSON.toJSONString(sql1.getParams()));
					
					//完全按照规则去抽
					Map<String, String> commParam = new HashMap<String, String>();
					commParam.put("schema", schema);
					commParam.put("currTime", currTime);
					commParam.put("endLoadTime", DateUtil.getCurrentDateStr());
				//	commParam.put("hasDynamicTime", dynamicTimeRange != null ? "1":"0");
					//名单抽取
					int size = extractOrderOriginalData(sql1, row, commParam, query ,logParam,taskLog);
					
					taskLog.setExtractNum(size);

					long duration = 0;
					try {
						sw.split();//获取耗时
						duration = sw.getSplitTime();
						taskLog.setDuration(duration);
					}catch (Exception e) {

					}
					
					
					taskLog.setExecResult(QcTaskLog.EXEC_SUCC);
					
					//获取接收到智能质检结果的数量
					if(CommonUtil.isNotBlank(taskId)) {
						String taskResultNumKey = "TASK_CALLBACK_NUM_" + taskId;
						Integer taskResultNum = cache.get(taskResultNumKey);
						if(taskResultNum != null) {
							taskLog.setZnResultNum(taskResultNum);
						}
					}

					//存储日志信息
					saveTaskLogRecord(schema, taskLogKey, taskLog, logParam);

					//添加追踪日志 --流程内记录CONTENT追踪内容
					logParam.put("DURATION",duration +" ms");

					LogUtils.addTaskTrackLogAutoType(user,schema,entId,busiOrderId,TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam);

					logParam.put("CREATE_TIME", DateUtil.addSecond(DateUtil.TIME_FORMAT,EasyCalendar.newInstance().getDateTime("-"),5));
					LogUtils.addTaskTrackLogAutoTypeAfterSecond(user,schema,entId,busiOrderId,TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
							,"工单质检任务抽取结束,共耗时 "+duration+" ms...",3);
				}

				//更新所有任务的上次执行时间
				if(CommonUtil.listIsNotNull(taskIds)) {
					query.execute("UPDATE " + schema + ".cc_qc_task set PREV_DATE=? "
					         + " WHERE ID in (" + taskIdSb.toString()+  ")"
					       , new Object[]{DateUtil.getCurrentDateStr()});
				}
			}else{
				LogUtils.addTaskTrackLogAutoType(user,schema,entId,SchemaUtil.getCcBusiOrderIdByEntId(entId), TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,new JSONObject()
						,"未查到任何在执行时间段内的工单任务,不执行任何操作.","未查到任何在执行时间段内的工单任务,不执行任何操作.");
				if(ServerContext.isDebug()) {
					logger.info(" 未查到任何在执行时间段内的工单任务,不执行任何操作.");
				}
			}
			String updateTaskSql = "UPDATE "+schema+".CC_QC_TASK SET STATE = 3 WHERE START_TIME<=? AND STATE!=3 AND END_TIME IS NOT NULL AND END_TIME<=?";
			query.execute(updateTaskSql, currTime,currTime);

			if(ServerContext.isDebug()) {
				logger.info(" 找出状态不为已关闭且实际已过期的任务,将其状态设置为已关闭! sql >> " + updateTaskSql + ", param >> [" + currTime + ", " + currTime + "]");
			}
		} catch (Exception e) {
			logger.error("执行工单质检任务失败，原因："+e.getMessage(),e);
		}
		return null;
	}
	
	private static int extractEmailOriginalData(EasySQL extract, EasyRow task, Map<String, String> commParam, EasyQuery query , JSONObject logParam, QcTaskLog taskLog) throws Exception {
		String taskId = task.getColumnValue("ID");
		String taskName = task.getColumnValue("TASK_NAME");
		String hasDynamicTime = commParam.get("hasDynamicTime");
		String entId = task.getColumnValue("ENT_ID");
		String busiOrderId = task.getColumnValue("BUSI_ORDER_ID");
		String schema = commParam.get("schema");
		//动态时间时为了防止一个任务重复质检相同的数据，限制每条记录只质检一次
		if(Constants.isOpenQcOnlyOne() ) {
			//如果开启每条记录只质检一次,只取未质检状态的数据
			extract.append("0", "AND T1.QC_STATE=?");
			taskLog.appendExecDesc("由于质检参数配置里开启了一条记录只运行抽取一次,本次将不再抽取已被抽取过的邮件记录.");
		}
		StringBuffer selecterSql = new StringBuffer("SELECT T1.ID AS SERIAL_ID,T1.FIRST_DISPATCH_TIME AS BEGIN_TIME,T1.CLOSE_TIME AS END_TIME,T1.HANDLE_ACC AS AGENT_ID,T1.TITLE,T1.EMAIL_FROM,T1.EMAIL_TO ");
		selecterSql.append(extract.getSQL());
		selecterSql.append(" order by T1.CLOSE_TIME ");
		if(ServerContext.isDebug()) {
			logger.info(" 执行数据抽取,任务名称["+taskName+"],任务ID["+taskId+"],抽取全媒体数据 sql:" +
					selecterSql.toString() + "param:" + JSON.toJSONString(extract.getParams()));
		}
		List<JSONObject>  serialList = query.queryForList(selecterSql.toString(), extract.getParams(), new JSONMapperImpl());

		taskLog.appendExecDesc("本次执行抽取到的数量:"+( serialList==null ? 0 :serialList.size()) );
		logParam.put("EXEC_SQL",selecterSql.toString() + ";param:" + JSON.toJSONString(extract.getParams()));
		LogUtils.addTaskTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskTrackTypeEnum.AUTO_EXT_QC_POOL,logParam
				,">>>执行邮件数据抽取...","");
		return addEmailTaskObj(serialList, task, commParam, false, null, query ,logParam, taskLog);
	}

	public static int addEmailTaskObj(List<JSONObject>  serialList, EasyRow task
			, Map<String, String> commParam, boolean isLimit, Map<String, Integer> angetNumMap, EasyQuery query ,JSONObject logParam,QcTaskLog taskLog) throws Exception {
		//抽取成功的总量
		int size = 0;
		//找不到坐席信息的总量
		int noAgentSize = 0;
		//找不到id的总量
		int noIdSize = 0;
		//非质检组里的坐席数据总量
		int noQcGroupAgent = 0;
		//已被该任务抽取过的总量
		int alreadyQcNum = 0;
		//已被该任务的父任务抽取过的总量
		int alreadyQcByParentNum = 0;

		String taskId = task.getColumnValue("ID");
		String taskName = task.getColumnValue("TASK_NAME");
		String channelType = task.getColumnValue("CHANNEL_TYPE");
		String entId = task.getColumnValue("ENT_ID");
		String busiOrderId = task.getColumnValue("BUSI_ORDER_ID");
		String examGroupId = task.getColumnValue("EXAM_GROUP_ID");
		String schema = commParam.get("schema");
		String currTime = commParam.get("currTime");
		String endLoadTime = commParam.get("endLoadTime");
		String maxEndTime = "";
		Set<String> setIds = new HashSet<String>();
		List<JSONObject> extObjectList = new ArrayList<>();
		Map<String ,Integer> errMap = new HashMap<>();
		//查询对应该质检任务对应的质检组是否有质检对象，没有则默认按抽取全部人员
		boolean isExtractAll = isExtractAllObj(query, schema, examGroupId);
		if(CommonUtil.listIsNotNull(serialList)) {
			//查询该任务对应的质检组里的所有坐席，该任务里没有设置坐席时，不执行任务抽取
			Set<String> angetAccSet = null;
			if(!isLimit) {
				angetAccSet = getTaskAgnets(examGroupId, schema, entId, busiOrderId, query, "AGENT_ACC");
			}else {
				angetAccSet = angetNumMap.keySet();
			}
			for (JSONObject easyRow : serialList) {
				String serialId = easyRow.getString("SERIAL_ID");
				String endTime = easyRow.getString("END_TIME");
				String beginTime = easyRow.getString("BEGIN_TIME");
				//邮件AGENT_ID存的是账号
				String agentId = easyRow.getString("AGENT_ID");
				JSONObject agent = CacheUtil.getCcUserCache().getCache(entId, busiOrderId, agentId);
				JSONObject taskObjLog = LogUtils.getTaskObjTrackLogParam(task, easyRow ,agent ,logParam);
				String agentInfo = agent == null ? "" : agent.getString("USERNAME")+"("+agent.getString("USER_ACCT")+")";
				if(StringUtils.compareIgnoreCase(endTime, maxEndTime) > 0) {
					maxEndTime = endTime;
				}
				if(agent == null) {
					noAgentSize ++;
					errMap.put(ErrorMsgEnum.NOT_AGENT.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.NOT_AGENT.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:找不到坐席【"+agentInfo+"】数据;ENT_ID=" + entId +", BUSI_ORDER_ID=" + busiOrderId + ", AGENT_ID=" + agentId);
					LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					logger.error("找不到坐席数据entId=" + entId +", busiOrderId=" + busiOrderId + ", agentId=" + agentId);
					continue;
				}
				if(StringUtils.isBlank(serialId) || setIds.contains(serialId)) {
					noIdSize ++;
					errMap.put(ErrorMsgEnum.DATA_EXCEP.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.DATA_EXCEP.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:该邮件记录数据异常;SERIAL_ID=" + serialId);
					LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					continue;
				}

				if (!isExtractAll){
					//如果质检组不是空，并且坐席不存在质检组里，不质检这条数据
					if(angetAccSet.size() > 0 && !angetAccSet.contains(agentId)) {
						noQcGroupAgent ++;
						errMap.put(ErrorMsgEnum.AGENT_NO_IN_GROUP.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.AGENT_NO_IN_GROUP.getCode())+"")+1);
						taskObjLog.put("CONTENT","抽取失败:坐席【"+agentInfo+"】不存在质检组里,不质检这条邮件记录。");
						LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
						continue;
					}
				}

				//判断话单是否抽取过，如果已经存在质检记录则不再处理
				if(existQcObj(taskId, serialId, schema, query)){
					alreadyQcNum ++ ;
					errMap.put(ErrorMsgEnum.IS_CHECKED.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.IS_CHECKED.getCode())+"")+1);
					taskObjLog.put("CONTENT","抽取失败:该邮件记录已被抽取过。");
					//LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
					continue;
				}
				//判断话单是否被父任务抽取过
			    if(existQcParentObj(taskId, serialId, schema, query,task.getColumnValue("PARENT_ID")).size()>0) {
					alreadyQcByParentNum ++ ;
			     	errMap.put(ErrorMsgEnum.IS_CHECKED.getCode(), CommonUtil.parseInt(errMap.get(ErrorMsgEnum.IS_CHECKED.getCode())+"")+1);
			     	taskObjLog.put("CONTENT","抽取失败:该邮件记录已被父任务抽取过。");
			     	logger.info("抽取失败:该邮件记录已被父任务抽取过。");
			     	//LogUtils.addTaskObjTrackLogAutoType(CommonUtils.getUser(logParam),schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
			     	continue;
			    }
				setIds.add(serialId);
				EasyRecord er = new EasyRecord(schema + ".CC_QC_TASK_OBJ", "ID");
				er.set("ID", RandomKit.randomStr());
				er.set("SERIAL_ID", serialId);
				er.set("ENT_ID", entId);  
				er.set("BUSI_ORDER_ID", busiOrderId);
				er.set("CREATE_TIME", currTime);
				er.set("UPDATE_TIME", currTime);
				er.set("TASK_ID", taskId);
				er.set("CHANNEL_TYPE", channelType == null ? "" : channelType);
				er.set("ZN_STATE", Constants.QC_STATE_EXTRACT);
				er.set("RG_STATE", Constants.QC_STATE_EXTRACT);
				//服务时间,通话或会话开始时间,格式如2020-12-12 12:00:00
				er.set("SERVICE_TIME", beginTime);
				er.set("DATE_ID", CommonUtil.parseInteger(currTime.substring(0,10).replaceAll("-", "")));
				if(agent!=null){
					er.set("AGENT_ID", agent.getString("USER_ID"));
					er.set("AGENT_ACC", agent.getString("USER_ACCT"));
					er.set("AGENT_NAME", agent.getString("USERNAME"));
					er.set("AGENT_DEPT_CODE", agent.getString("DEPT_CODE"));
				}
				
				query.save(er);

				//将邮件记录改为已质检状态
				EasyRecord updateMedia = new EasyRecord(schema + ".C_EMAIL_SESSION", "ID");
				updateMedia.setPrimaryValues(serialId);
				updateMedia.set("QC_STATE", Constants.QC_STATE_EXTRACT);
				query.update(updateMedia);

				size += 1;
				taskObjLog.put("CONTENT","抽取成功:该邮件记录被抽取到【"+taskName+"】当中。");
				LogUtils.addTaskObjTrackLog(schema,entId,busiOrderId, TrackTypeLog.TaskObjTrackTypeEnum.EXT_TO_POOL,taskObjLog);
				extObjectList.add(easyRow);
			}
			if(size == 0) {
				maxEndTime = endLoadTime;
			}
			QueryUtil.uodateQcTaskLoadTime(schema, taskId, maxEndTime, query);
			taskLog.setExtractEndTime(maxEndTime);
			logParam.put("CONTENT",logParam.getString("CONTENT")+">>>质检任务抽取成功,过滤不符合条件的数据后,共抽取["+size+"条]数据");
			taskLog.appendExecDesc("本次质检任务抽取后剔除的数据总量:"+(noAgentSize+noIdSize+noQcGroupAgent+alreadyQcNum+alreadyQcByParentNum)
					+",包含: 记录找不到坐席的总量:"+noAgentSize
					+",记录无ID的总量:"+noIdSize
					+",非质检组里的坐席数据总量:"+noQcGroupAgent
					+",已被该任务抽取过的总量:"+alreadyQcNum
					+",已被该任务的父任务抽取过的总量:"+alreadyQcByParentNum
					+"<br>"
			);
		}else {
			//抽取不到数据时更新数据加载时间到endLoadTime
			QueryUtil.uodateQcTaskLoadTime(schema, taskId, endLoadTime, query);
			taskLog.setExtractEndTime(endLoadTime);
			logParam.put("CONTENT",">>>质检任务抽取完成,该抽取范围内没有符合条件的数据。");
		}
		taskLog.appendExecDesc("本次抽取后最终总量:"+size);
		LogUtils.addErrMsgLog(logParam, entId, busiOrderId, schema, errMap);
		return size;
	}

	/**
	 * 通用保存日志信息方法
	 * @param schema
	 * @param taskLogKey
	 * @param taskLog
	 * @param logParam
	 */
	private static void saveTaskLogRecord(String schema, String taskLogKey, QcTaskLog taskLog, JSONObject logParam) {
		String taskLogId = cache.get(taskLogKey);
		if(CommonUtil.isNotBlank(taskLogId)) {
			taskLog.setId(taskLogId);
			cache.delete(taskLogKey);
			taskLog.saveOrUpdate(false, schema);
		}else {
			taskLogId = logParam.getString("TASK_EXEC_ID");
			taskLog.setId(taskLogId);
			taskLog.saveOrUpdate(true, schema);
			cache.put(taskLogKey, taskLogId, 30 * 60);
		}
	}

	/**
	 * 根据质检组数量判断是否抽取全部数据
	 * @param query
	 * @param schema
	 * @param examGroupId
	 * @return
	 * @throws SQLException
	 */
	private static boolean isExtractAllObj(EasyQuery query, String schema, String examGroupId) throws SQLException {
		EasySQL sql = new EasySQL("SELECT count(*) FROM " + schema + ".CC_QC_GROUP_AGENT t1");
		sql.append(examGroupId,"WHERE t1.EXAM_GROUP_ID = ?");
		int GroupAgentSize = query.queryForInt(sql.getSQL(), sql.getParams());
		return GroupAgentSize == 0;
	}
}
