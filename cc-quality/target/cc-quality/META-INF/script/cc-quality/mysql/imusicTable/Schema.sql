-- ay<PERSON><PERSON>_ycbusi_ekf.cc_qc_capacity definition

CREATE TABLE `cc_qc_capacity`
(
    `ID`            varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
    `SCORE`         decimal(10, 2)                   DEFAULT NULL COMMENT '得分',
    `TOTAL_SCORE`   decimal(10, 2)                   DEFAULT NULL COMMENT '总分',
    `ENT_ID`        varchar(30) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '企业ID',
    `BUSI_ORDER_ID` varchar(64) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '订购Id',
    `REMARK`        varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
    `START_TIME`    varchar(19) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '公司智能质检没有先保留',
    `END_TIME`      varchar(19) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '公司智能质检没有先保留',
    `IS_VETO`       varchar(20) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '是否一票否决，0-否 1-是',
    `SERIAL_ID`     varchar(64) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '话单表ID',
    `QC_TIME`       varchar(19) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '质检时间，从智能质检平台获取结果的时间',
    `RESULT_ID`     varchar(64) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '质检结果ID（质检平台的唯一标识）',
    `ZN_CLASS_ID`   varchar(64) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '智能质检规则ID',
    `CALL_TYPE`     varchar(20) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '渠道类型 1:话务 2:新媒体 4:工单',
    `AGENT_ACC`     varchar(30) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '坐席账号',
    `AGENT_NAME`    varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '坐席姓名',
    PRIMARY KEY (`ID`) USING BTREE,
    KEY             `IDX_CC_QC_CAPACITY_1` (`SERIAL_ID`,`ZN_CLASS_ID`,`ENT_ID`,`BUSI_ORDER_ID`) USING BTREE,
    KEY             `IDX_CC_QC_CAPACITY_2` (`RESULT_ID`) USING BTREE,
    KEY             `IDX_CC_QC_CAPACITY_3` (`QC_TIME`) USING BTREE,
    KEY             `IDX_CC_QC_CAPACITY_4` (`ZN_CLASS_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='智能质检表,一条话单可以被多个任务关联，但是根据话单ID+智能质检规则，只会有一条记录';

-- ayykefu_stat.cc_dim_date definition
CREATE TABLE `cc_dim_date`
(
    `DATE_ID`         int(11) NOT NULL COMMENT '日期ID，格式：yyyymmdd',
    `DATE_VALUE`      varchar(19) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '日期值，格式：yyyy-mm-dd',
    `YEAR`            int(11) DEFAULT NULL COMMENT '年份，格式：yyyy，例如：2019',
    `QUARTER`         int(11) DEFAULT NULL COMMENT '季度，第N季度',
    `MONTH`           int(11) DEFAULT NULL COMMENT '月份',
    `WEEK`            int(11) DEFAULT NULL COMMENT '周',
    `QUARTER_IN_YEAR` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '年季度，格式：2019年1季度',
    `MONTH_IN_YEAR`   varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '年月，格式：2019年1月',
    `WEEK_IN_YEAR`    varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '年周，格式：2019年第1周',
    PRIMARY KEY (`DATE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='日期维度信息';
