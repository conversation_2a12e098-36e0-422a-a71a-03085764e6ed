<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<application id="cc-quality" name="质检" package-by="liaoyikai" package-time="2025-08-26 13:43:57" resTable="ycBusiRes#007" version="3.4#20250826-1">
    <datasources>
        <datasource description="数据源(写1)" isnull="true" name="yc-wirte-ds-1"/> 
        <datasource description="数据源(写2)" isnull="true" name="yc-wirte-ds-2"/> 
        <datasource description="数据源(读)" isnull="true" name="yc-read-ds"/>
    </datasources>
    <description>
        3.5#20250703-1 待质检列表获取渠道-任务数据（多级下拉）接口增加过滤逻辑，仅筛选启动或者暂停中的任务
        3.5#20250702-2 智能质检结果新增命中话术字段
    </description>
</application>
