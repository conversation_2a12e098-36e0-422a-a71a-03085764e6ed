<template>
	<div class="orderMsg">
		<!-- <div class="order-top-box">
			<div class="order-top-orderId">{{ getI18nValue('工单：') }}{{ orderMessage.orderId }}</div>
			<div class="baseflex">
				<div class="baseflex mr-r32">
					<div class="order-top-item mr-r16">{{ getI18nValue('催办次数') }}</div>
					<el-tag size="mini" type="warning">{{ orderMessage.count1 }}</el-tag>
				</div>
				<div class="baseflex">
					<div class="order-top-item mr-r16">{{ getI18nValue('督办次数') }}</div>
					<el-tag size="mini" type="danger">{{ orderMessage.count2 }}</el-tag>
				</div>
			</div>
		</div> -->
		<div style="height:208px;" v-if="serialIdList.length > 0">
			<el-tabs v-model="selectSerialId">
				<el-tab-pane :lazy="true" :label="'录音-' + (index + 1)" :name="item"
					v-for="(item, index) in serialIdList" :key="index">
					<media-player :phone="setMediaMsg.custPhone1 || setMediaMsg.custPhone" :serial-id="item" :key="item"></media-player>
				</el-tab-pane>
			</el-tabs>
		</div>
		<el-tabs v-model="orderId">
			<template v-for="(item, index) in orderList">
				<el-tab-pane :label="item.CREATE_NAME" :name="item.ID">
					<iframe :src="item.baseUrl" id="orderMsg" :class="{ 'hasMedia': serialIdList.length > 0 }"
						style="width:100%;height:calc(100vh - 200px);border: unset;"></iframe>
				</el-tab-pane>
			</template>
		</el-tabs>
	</div>
</template>
<script>
module.exports = {
	name: 'orderMsg',
	components: {
		'media-player': httpVueLoader(
			'/cc-quality/pages/qualityControl/components/media-player.vue'
		),
	},
	props: {
		params: {
			type: String,
			default: function () {
				return {}
			}
		},
	},
	data() {
		return {
			orderMessage: {},
			baseUrl: '',
			orderId: '',
			selectSerialId: '',
			orderMainData: {},
			orderList: [],
			serialIdList: [],
			setMediaMsg:{}
		}
	},
	mounted() {
		this.initFrame()
		window.setMediaList = this.setMediaList
	},
	methods: {
		initFrame() {
			let data = {
				objId: this.params.objid,
				taskId: this.params.taskId
			}
			yq.remoteCall('/cc-quality/servlet/qcTask?action=GetOredeTaskData', data).then(res => {
				if (res.objData && JSON.stringify(res.objData) != '{}') {
					this.$emit('setcustmsg1', res.objData)
				}
				else {
					console.error('获取录音数据失败！')
				}
				if (res.orderList && res.orderList.length != 0) {
					this.orderList = res.orderList
					this.orderId = res.orderList[0].ID
					this.orderList.forEach((item, index) => {
						this.orderList[index].baseUrl = '/cc-eorder/servlet/ordersearch?action=ToViewNowOrder&orderId=' + item.ID
					})
					// this.$nextTick(() => {
					// 	this.addJT()
					// })
				}
				if (res.orderMainData && JSON.stringify(res.orderMainData) != '{}') {
					this.orderMainData = res.orderMainData
					this.$emit('setcustmsg', this.orderMainData)
				}
				if (res.taskData && JSON.stringify(res.taskData) != '{}') {
					this.$emit('set-task-data', res.taskData)
				}

			})
		},
		getOrderList() {
			return this.orderList
		},
		addJT() {
			var iframe = document.getElementById('orderMsg')
			var that = this
			this.serialIdList = []
			iframe.onload = function () {
				var frameDoc = iframe.contentWindow
				var phone1 = frameDoc.getValueByKeyName('custPhone1')
				var phone2 = frameDoc.getValueByKeyName('custPhone')
				that.getMediaList(phone1)
				that.getMediaList(phone2)
			}
		},
		setMediaList(obj) {
			console.log('setMediaList', obj)
			this.setMediaMsg = obj
			var phone1 = obj.custPhone1
			var phone2 = obj.custPhone
			if (yq.isNull(phone1) && yq.isNull(phone2)) {
				console.error('phone1,phone2 is null', obj)
				return
			}
			if (!yq.isNull(phone1)) {
				this.getMediaList(phone1)
			}
			if (!yq.isNull(phone2) && (phone1 != phone2)) {
				this.getMediaList(phone2)
			}
			// else if (!yq.isNull(phone2)) this.getMediaList(phone2)
		},
		getMediaList(phone) {
			if (yq.isNull(phone)) return
			yq.remoteCall('/cc-quality/webcall?action=QcTaskObjDao.OrderMessageByPhone', { phone: phone }).then(res => {
				if (res.state == 1) {
					this.serialIdList = [...new Set([...this.serialIdList, ...res.data.serialIdList])]
					if (this.serialIdList.length > 0) {
						this.selectSerialId = this.serialIdList[0]
					}
				}
				// else {
				// 	this.$message.error('获取录音列表失败！')
				// }
			})
		},
	}
}
</script>
<style scoped>
.hasMedia {
	height: calc(100vh - 450px) !important;
}

.orderMsg {
	padding: 0 24px 24px 24px;
	height: calc(100% - 48px);
	width: calc(100% - 48px);
	display: flex;
	flex-direction: column;
}

.order-top-box {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.order-top-orderId {
	color: #262626;
	font-size: 24px;
	font-weight: bold;
	line-height: 36px;
}
</style>
