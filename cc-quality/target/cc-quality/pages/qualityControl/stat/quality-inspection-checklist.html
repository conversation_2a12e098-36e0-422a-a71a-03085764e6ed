<!DOCTYPE html>
<html>

<head>
    <title>质检清单</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport"
        content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <!-- 基础的 css js 资源 -->
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0">
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.0">
    <link rel="stylesheet" href="/cc-quality/static/css/qualityControl.css?v=1.0.1">
    <style>
    </style>
</head>


<body class="yq-page-full vue-box">
    <div id="qualityInspectionChecklist" class="flex yq-table-page">
        <div class="flex yq-card">
            <div class="title-box">
                <div class="title">{{getI18nValue('质检清单')}}</div>
            </div>
            <div class="card-content">
                <el-form :inline="false" :model="formData" ref="form" class="search-form" size="small"
                    label-width="100px">
                    <el-form-item :label="getI18nValue('质检工单创建时间')" prop="date">
                        <el-date-picker v-model="formData.date" type="datetimerange" align="right" unlink-panels
                            range-separator="至" size="small" :start-placeholder="getI18nValue('开始日期')"
                            :end-placeholder="getI18nValue('结束日期')" :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd HH:mm:ss">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('质检方式')" prop="qcOrder">
                        <el-select v-model="formData.qcOrder" :placeholder="getI18nValue('质检方式')" multiple clearable>
                            <el-option v-for="(key,value) of QC_ORDER" :label="key" :value="value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('工单类型')" prop="channelType">
                        <el-select v-model="formData.channelType" :placeholder="getI18nValue('工单类型')" multiple
                            clearable>
                            <el-option v-for="(key,value) of channelOptions" :label="key" :value="value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('客户姓名')" prop="agentName" v-show="show">
                        <el-input v-model="formData.agentName" :placeholder="getI18nValue('客户姓名')" clearable></el-input>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('坐席姓名')" prop="inspectorName" v-show="show">
                        <el-input v-model="formData.inspectorName" :placeholder="getI18nValue('坐席姓名')" clearable></el-input>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('质检工单状态')" prop="objState" v-show="show">
                        <el-select v-model="formData.objState" :placeholder="getI18nValue('质检工单状态')" multiple clearable>
                            <el-option v-for="(key,value) of qcStatusOptions" :label="key" :value="value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('人工质检结果')" prop="rgResult" v-show="show">
                        <el-select v-model="formData.rgResult" :placeholder="getI18nValue('请选择')" multiple clearable>
                            <el-option v-for="(key,value) of passFlagList" :label="key" :value="value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('智能质检结果')" prop="znResult" v-show="show">
                        <el-select v-model="formData.znResult" :placeholder="getI18nValue('请选择')" multiple clearable>
                            <el-option v-for="(key,value) of passFlagList" :label="key" :value="value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('投诉号码')" prop="caller" v-show="show">
                        <el-input v-model="formData.caller" :placeholder="getI18nValue('投诉号码')" clearable></el-input>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('用户评分')" prop="satisfyId" v-show="show">
                        <el-select v-model="formData.satisfyId" :placeholder="getI18nValue('请选择')" multiple clearable>
                            <el-option v-for="(key,value) of userScoreOptions" :label="key" :value="value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item class="btns" label-width="10px">
                        <el-button type="primary" size="small" icon="el-icon-search"
                            @click="doSearch()">{{getI18nValue('查询')}}</el-button>
                        <el-button type="primary" plain size="small" icon="el-icon-refresh"
                            @click="reset">{{getI18nValue('重置')}}</el-button>
                        <el-button type="primary" plain size="small" icon="el-icon-arrow-down"
                            @click="show = !show ">{{getI18nValue('高级查询')}}</el-button>
                    </el-form-item>
                </el-form>
                <div class="yq-table">
                    <el-table :data="tableData" style="width: 100%" height="100%" v-loading="pageNav.loading">
                        <el-table-column type="index" :label="getI18nValue('序号')" width="50">
                        </el-table-column>
                        <el-table-column prop="ORDER_NO" :label="getI18nValue('工单号(客服工单号)')" width="180"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <el-link type="primary" :underline="false"
                                    @click="handleDetail(scope.row)">{{scope.row.ORDER_NO}}</el-link>
                            </template>
                        </el-table-column>
                        <el-table-column prop="CALLER" :label="getI18nValue('投诉号码(来电号码)')" width="180"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="TASK_NAME" :label="getI18nValue('任务名称')" width="180"
                                         show-overflow-tooltip></el-table-column>
                        <el-table-column prop="QC_ORDER" :label="getI18nValue('质检方式')" width="180"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span>{{COL_QC_ORDER[scope.row.QC_ORDER] || '-'}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="CHANNEL_TYPE" :label="getI18nValue('工单类型')" width="180"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span>{{channelOptions[scope.row.CHANNEL_TYPE] || '-'}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="CREATE_TIME" :label="getI18nValue('工单创建时间')" width="180"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="AGENT_NAME" :label="getI18nValue('员工姓名（被质检人）')" width="180"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="INSPECTOR_NAME" :label="getI18nValue('质检员姓名')" width="180"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="RG_QC_RESULT" :label="getI18nValue('人工质检结果')" width="180"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span>{{passFlagList[scope.row.RG_QC_RESULT] || '-'}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="ZN_QC_RESULT" :label="getI18nValue('智能质检结果')" width="180"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span>{{passFlagList[scope.row.ZN_QC_RESULT] || '-'}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="QC_CREATE_TIME" :label="getI18nValue('质检工单创建时间')" width="180"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="RG_QC_TIME" :label="getI18nValue('人工质检完成时间')" width="180"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="ZN_QC_TIME" :label="getI18nValue('智能质检完成时间')" width="180"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="OBJ_STATE" :label="getI18nValue('质检工单状态')" width="180"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span>{{qcStatusOptions[scope.row.OBJ_STATE] || '-'}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="EVALUATE_EXPLAIN" :label="getI18nValue('存在问题概述')" width="180"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="SATISF_NAME" :label="getI18nValue('用户评分')" width="180"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span>{{scope.row.SATISF_NAME || '-'}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column :label="getI18nValue('操作')" width="250" fixed="right">
                            <template slot-scope="scope">
                                <el-link type="primary" :underline="false"
                                    @click="handleQc(scope.row)">{{getI18nValue('质检')}}</el-link>
                                <el-link type="primary" :underline="false"
                                    @click="handleDetail(scope.row)">{{getI18nValue('查看详情')}}</el-link>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" background
                        :current-page="pageNav.pageIndex" :page-sizes="[15, 30, 50, 100]" :page-size="pageNav.pageSize"
                        layout="total, sizes, prev, pager, next, jumper" :total="pageNav.total">
                    </el-pagination>
                </div>
            </div>
        </div>
    </div>
    <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
    <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
    <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
    <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
    <script src="/cc-quality/static/js/time.js"></script>
    <script src="/cc-quality/static/lib/wavesurfer/wavesurfer.js"></script>
    <script src="/cc-quality/static/lib/wavesurfer/wavesurfer.timeline.min.js"></script>
    <script type="text/javascript" src="/cc-quality/static/js/my_i18n.js?v=2021110901"></script>
    <script type="text/javascript" src="/cc-base/static/js/i18n.js?v=20140426"></script>
    <script>
        var qualityInspectionChecklist = new Vue({
            components: {},
            el: '#qualityInspectionChecklist',
            data: function () {
                return {
                    formData: {
                        date: [],
                        endStartDate: '',
                        beginStartDate: '',
                        qcOrder: [],
                        agentName: '',
                        inspectorName: '',
                        channelType: [],
                        objState: [],
                        rgResult: [],
                        znResult: [],
                        caller: '',
                        satisfyId: []
                    },
                    QC_ORDER: {
                        '1': '智能+人工',
                        '2': '仅人工',
                        '3': '仅智能',
                    },
                    COL_QC_ORDER: {},
                    pickerOptions: {
                        shortcuts: [{
                            text: getI18nValue('最近一周'),
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                                picker.$emit('pick', [start, end]);
                            }
                        }, {
                            text: getI18nValue('最近一个月'),
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                                picker.$emit('pick', [start, end]);
                            }
                        }, {
                            text: getI18nValue('最近三个月'),
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                                picker.$emit('pick', [start, end]);
                            }
                        }]
                    },
                    show: false,
                    channelOptions: {},
                    qcStatusOptions: {},
                    userScoreOptions: {},
                    tableData: [],
                    pageNav: {
                        loading: false,
                        pageIndex: 1,
                        pageSize: 10,
                        total: 0
                    },
                    checkLayer: null
                }
            },
            watch: {
                'formData.date': {
                    handler(n) {
                        if (yq.isNull(n) && n.length > 0) {
                            this.formData.beginStartDate = ''
                            this.formData.endStartDate = ''
                            return;
                        }
                        this.formData.beginStartDate = n[0]
                        this.formData.endStartDate = n[1]
                    }
                },
            },
            mounted() {
                this.getDict()
                this.formData.date = [getRecentlyOneMonthStartDate() + ' 00:00:00', getTodayDate() + ' 23:59:59']
                this.formData.beginStartDate = getRecentlyOneMonthStartDate() + ' 00:00:00'
                this.formData.endStartDate = getTodayDate() + ' 23:59:59'
                this.doSearch()
                window.closeLayer = this.closeLayer
            },
            methods: {
                closeLayer(tp) {
                    window.layer.close(this.checkLayer)
                    this.doSearch()
                },
                getPage(callback) {
                    this.doSearch(callback)
                },
                getDict() {
                    let data = {
                        params: {},
                        controls: [
                            "QcCommonDao.getDict(QC_CHANNEL_TYPE)",
                            "QcCommonDao.getDict(QC_OBJ_STATE)",
                            "QcCommonDao.getDict(QC_RESULT)",
                            "QcCommonDao.getDict(VOICE_SATISF)",
                            "QcCommonDao.getDict(QC_ORDER)",
                        ]
                    }
                    let _this = this
                    yq.daoCall(data, null, {
                        "contextPath": "/cc-quality"
                    }).then(function (res) {
                        _this.COL_QC_ORDER = res['QcCommonDao.getDict(QC_ORDER)'].data
                        _this.userScoreOptions = res['QcCommonDao.getDict(VOICE_SATISF)'].data
                        _this.passFlagList = res['QcCommonDao.getDict(QC_RESULT)'].data
                        _this.qcStatusOptions = res['QcCommonDao.getDict(QC_OBJ_STATE)'].data
                        _this.channelOptions = res['QcCommonDao.getDict(QC_CHANNEL_TYPE)'].data
                    })
                },
                handleSizeChange(size) {
                    this.pageNav.pageSize = size
                    this.getList()
                },
                handleCurrentChange(page) {
                    this.pageNav.pageIndex = page
                    this.getList()
                },
                doSearch(callback) {
                    this.pageNav.pageIndex = 1
                    this.getList(callback)
                },
                getList(callback) {
                    this.pageNav.loading = true
                    let data = {
                        ...this.formData,
                        pageIndex: this.pageNav.pageIndex,
                        pageSize: this.pageNav.pageSize,
                        pageType: '3'
                    }

                    // 处理数组字段转为逗号分隔的字符串
                    const arrayToString = (field) => {
                        if (data[field] && data[field].length) {
                            data[field] = data[field].join(',')
                        } else {
                            data[field] = ''
                        }
                    }

                    // 批量处理所有需要转换的字段
                    ['channelType', 'qcOrder', 'rgResult', 'znResult', 'objState', 'satisfyId'].forEach(arrayToString)

                    delete data.date
                    yq.remoteCall('/cc-quality/webcall?action=QcTaskObjDao.getAllQualityList', data)
                        .then(res => {
                            if (res.state == 1) {
                                this.tableData = res.data
                                this.pageNav.total = res.totalRow
                                if (!callback) return
                                if (res.data.length != 0) {
                                    callback(res.data[0])
                                } else {
                                    callback({})
                                }
                            } else {
                                this.$message.error(res.msg)
                            }
                        }).finally(() => {
                            this.pageNav.loading = false
                        })
                },
                reset() {
                    this.$refs.form.resetFields()
                },
                // 查看详情
                handleDetail(row) {
                    const { CHANNEL_TYPE, OBJ_ID, QC_RESULT_ID, SERIAL_ID, ZN_CLASS_ID, TEMPLATE_ID, CLASS_ID, TASK_ID, EXAM_GROUP_ID } = row

                    // 通用弹窗配置
                    const layerShowConfig = {
                        type: 2,
                        title: getI18nValue('质检详情'),
                        area: ['80%', '80%'],
                        offset: CHANNEL_TYPE === 1 ? undefined : '20px',
                        maxmin: true,
                        full: true,
                        shadeClose: false,
                        moveOut: true
                    }
                    const baseData = { objId: OBJ_ID, qcResultId: QC_RESULT_ID, serialId: SERIAL_ID, znClassId: ZN_CLASS_ID }
                    // 渠道类型配置
                    const channelConfig = {
                        // 语音
                        1: {
                            url: '/cc-quality/pages/qualityControl/result/qc-result-voice-detail.html',
                            data: { ...baseData, templateId: TEMPLATE_ID, channelType: CHANNEL_TYPE }
                        },
                        // 全媒体
                        '2': {
                            url: '/cc-quality/pages/qualityControl/result/qc-result-media-detail.html',
                            data: { ...baseData, templateId: TEMPLATE_ID }
                        },
                        // 邮件
                        3: {
                            url: '/cc-quality/pages/qualityControl/result/qc-result-email-detail.html',
                            data: { ...baseData, isRead: true }
                        },
                        // 工单
                        4: {
                            url: '/cc-quality/pages/qualityControl/result/qc-result-order-detail.html',
                            data: { ...baseData, groupId: EXAM_GROUP_ID, taskId: TASK_ID, isRead: true }
                        },
                        // 第三方
                        9: {
                            url: '/cc-quality/pages/qualityControl/result/qc-result-thrity-detail.html',
                            data: { ...baseData, templateId: TEMPLATE_ID, groupId: EXAM_GROUP_ID, classId: CLASS_ID }
                        },
                        // 默认（媒体）
                        default: {
                            url: '/cc-quality/pages/qualityControl/result/qc-result-media-detail.html',
                            data: { ...baseData, templateId: TEMPLATE_ID, groupId: EXAM_GROUP_ID, taskId: TASK_ID, channelType: CHANNEL_TYPE }
                        }
                    }

                    // 获取当前渠道配置或默认配置
                    const config = channelConfig[CHANNEL_TYPE] || channelConfig.default

                    // 打开详情页
                    yq.layerShow(layerShowConfig, config.url, config.data)
                },
                // 质检
                handleQc(row) {
                    const { CHANNEL_TYPE, OBJ_ID, QC_RESULT_ID, SERIAL_ID, ZN_CLASS_ID, TEMPLATE_ID, CLASS_ID, TASK_ID, EXAM_GROUP_ID } = row

                    // 通用弹窗配置
                    const layerShowConfig = {
                        title: getI18nValue('质检'),
                        offset: 't', // 如果去掉,就是默认居中
                        area: ['100%', '100%'],
                        type: 2, // 2或者iframe为iframe方式打开弹层,1或div为div方式
                    }

                    // 通用数据参数
                    const baseData = {
                        groupId: EXAM_GROUP_ID,
                        serialId: SERIAL_ID,
                        objId: OBJ_ID,
                        classId: CLASS_ID,
                        znClassId: ZN_CLASS_ID,
                        qcResultId: QC_RESULT_ID,
                        taskId: TASK_ID,
                        layer: 'checkLayer'
                    }

                    // 渠道类型配置映射
                    const channelConfig = {
                        // 语音
                        '1': {
                            url: '/cc-quality/pages/qualityControl/result/qc-result-voice-check.html',
                            data: baseData
                        },
                        // 全媒体
                        '2': {
                            url: '/cc-quality/pages/qualityControl/result/qc-result-media-check.html',
                            data: baseData
                        },
                        // 邮件
                        '3': {
                            url: '/cc-quality/pages/qualityControl/result/qc-result-email-check.html',
                            data: { ...baseData, isRead: true }
                        },
                        // 工单
                        '4': {
                            url: '/cc-quality/pages/qualityControl/result/qc-result-order-check.html',
                            data: { ...baseData, type: "1" }
                        },
                        // 第三方
                        '9': {
                            url: '/cc-quality/pages/qualityControl/result/qc-result-thrity-check.html',
                            data: { ...baseData, type: "1", templateId: TEMPLATE_ID }
                        }
                    }

                    // 获取当前渠道配置
                    const config = channelConfig[CHANNEL_TYPE]

                    // 如果配置存在，打开质检弹窗
                    if (config) {
                        this.checkLayer = yq.layerShow(layerShowConfig, config.url, config.data)
                    }
                }
            },
        })
    </script>
</body>

</html>