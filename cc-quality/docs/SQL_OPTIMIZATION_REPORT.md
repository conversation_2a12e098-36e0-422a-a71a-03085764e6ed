# qcProblemStatByAi方法SQL优化报告

## 问题描述
`QcResultDao.qcProblemStatByAi()` 方法执行时出现严重性能问题：
- 执行时间：127840ms（约128秒）
- 错误：`CommunicationsException: Communications link failure`
- 原因：SQL查询超时导致数据库连接断开

## 性能分析

### 原始执行计划问题
```
id  select_type  table        type  possible_keys           key   rows    filtered  Extra
1   SIMPLE       t1           ALL   IDX_CC_DC_CAPACITY_3   null  273597  50        Using where; Using temporary
2   SIMPLE       cc_dim_date  ALL   null                   null  2580    100       Using where; Using join buffer
```

### 主要性能瓶颈
1. **全表扫描**：`substr(t1.QC_TIME, 1, 10)` 函数导致索引失效
2. **JOIN性能差**：跨库LEFT JOIN使用join buffer，效率低
3. **N+1查询问题**：主查询后对每个结果执行2次子查询
4. **临时表**：GROUP BY操作需要创建临时表

## 优化方案

### 1. 数据库索引优化
```sql
-- 为cc_qc_capacity表添加复合索引
ALTER TABLE cc_qc_capacity ADD INDEX idx_qc_time_agent (QC_TIME, AGENT_NAME);

-- 为cc_qc_details表添加复合索引  
ALTER TABLE cc_qc_details ADD INDEX idx_capacity_score (CAPACITY_ID, SCORE);
```

### 2. 主查询SQL重写
**优化前：**
```sql
LEFT JOIN CC_DIM_DATE cc_dim_date on substr(t1.QC_TIME, 1, 10) = cc_dim_date.DATE_VALUE
WHERE 1 = 1 AND t1.QC_TIME >= ? AND t1.QC_TIME <= ?
```

**优化后：**
```sql
LEFT JOIN CC_DIM_DATE cc_dim_date on DATE(t1.QC_TIME) = cc_dim_date.DATE_VALUE  
WHERE t1.QC_TIME >= ? AND t1.QC_TIME <= ?
```

**改进点：**
- 去除`substr`函数，使用`DATE`函数
- 简化WHERE条件，去除无意义的`1=1`
- 允许使用QC_TIME字段上的索引

### 3. 消除N+1查询问题
**优化前：**
- 主查询获取基础数据
- 对每条记录执行2次子查询（致命/非致命统计）
- 总查询次数：1 + N*2

**优化后：**
- 主查询获取基础数据
- 批量查询获取所有致命错误统计
- 批量查询获取所有非致命错误统计  
- 总查询次数：3次

### 4. 代码重构
- 新增`getBatchStatItemsByAi`方法实现批量查询
- 保留原`getStatItemSqlByAi`方法并标记为`@Deprecated`
- 优化数据处理逻辑，减少内存占用

## 预期效果

### 性能提升
- **执行时间**：从127秒降低到5秒以内
- **性能提升**：>95%
- **数据库负载**：显著降低
- **连接超时**：彻底解决

### 资源优化
- **查询次数**：从1+N*2次降低到3次
- **索引利用**：充分利用新建的复合索引
- **内存使用**：减少临时表和中间结果集

## 测试验证

### 测试用例
创建了`QcResultDaoPerformanceTest`类进行性能测试：
- `testOptimizedQcProblemStatByAi()`：验证优化后的性能
- `testSqlGeneration()`：验证SQL语句正确性
- `performanceBenchmark()`：性能基准对比

### 验证步骤
1. 执行数据库索引创建脚本
2. 部署优化后的代码
3. 运行性能测试用例
4. 监控实际业务场景的执行时间

## 风险评估

### 低风险
- 保留了原有方法作为备用
- 新增索引不影响现有功能
- SQL逻辑保持一致，只是性能优化

### 注意事项
- 新索引会占用额外存储空间
- 数据插入时索引维护会有轻微性能影响
- 建议在业务低峰期执行索引创建

## 部署建议

### 部署顺序
1. 先执行索引创建SQL（业务低峰期）
2. 部署优化后的代码
3. 监控系统性能和错误日志
4. 如有问题可快速回滚

### 监控指标
- 方法执行时间
- 数据库连接池状态
- SQL执行计划
- 系统资源使用率

---
**优化完成时间：** 2025-08-26  
**预期上线时间：** 待定  
**负责人：** AI Assistant
