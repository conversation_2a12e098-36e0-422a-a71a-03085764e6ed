-- ay<PERSON><PERSON>_ycbusi_ekf.cc_qc_capacity definition

CREATE TABLE `cc_qc_capacity`
(
    `ID`            varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
    `SCORE`         decimal(10, 2)                   DEFAULT NULL COMMENT '得分',
    `TOTAL_SCORE`   decimal(10, 2)                   DEFAULT NULL COMMENT '总分',
    `ENT_ID`        varchar(30) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '企业ID',
    `BUSI_ORDER_ID` varchar(64) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '订购Id',
    `REMARK`        varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
    `START_TIME`    varchar(19) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '公司智能质检没有先保留',
    `END_TIME`      varchar(19) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '公司智能质检没有先保留',
    `IS_VETO`       varchar(20) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '是否一票否决，0-否 1-是',
    `SERIAL_ID`     varchar(64) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '话单表ID',
    `QC_TIME`       varchar(19) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '质检时间，从智能质检平台获取结果的时间',
    `RESULT_ID`     varchar(64) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '质检结果ID（质检平台的唯一标识）',
    `ZN_CLASS_ID`   varchar(64) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '智能质检规则ID',
    `CALL_TYPE`     varchar(20) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '渠道类型 1:话务 2:新媒体 4:工单',
    `AGENT_ACC`     varchar(30) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '坐席账号',
    `AGENT_NAME`    varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '坐席姓名',
    PRIMARY KEY (`ID`) USING BTREE,
    KEY             `IDX_CC_QC_CAPACITY_1` (`SERIAL_ID`,`ZN_CLASS_ID`,`ENT_ID`,`BUSI_ORDER_ID`) USING BTREE,
    KEY             `IDX_CC_QC_CAPACITY_2` (`RESULT_ID`) USING BTREE,
    KEY             `IDX_CC_QC_CAPACITY_3` (`QC_TIME`) USING BTREE,
    KEY             `IDX_CC_QC_CAPACITY_4` (`ZN_CLASS_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='智能质检表,一条话单可以被多个任务关联，但是根据话单ID+智能质检规则，只会有一条记录';

-- ayykefu_stat.cc_dim_date definition
CREATE TABLE `cc_dim_date`
(
    `DATE_ID`         int(11) NOT NULL COMMENT '日期ID，格式：yyyymmdd',
    `DATE_VALUE`      varchar(19) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '日期值，格式：yyyy-mm-dd',
    `YEAR`            int(11) DEFAULT NULL COMMENT '年份，格式：yyyy，例如：2019',
    `QUARTER`         int(11) DEFAULT NULL COMMENT '季度，第N季度',
    `MONTH`           int(11) DEFAULT NULL COMMENT '月份',
    `WEEK`            int(11) DEFAULT NULL COMMENT '周',
    `QUARTER_IN_YEAR` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '年季度，格式：2019年1季度',
    `MONTH_IN_YEAR`   varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '年月，格式：2019年1月',
    `WEEK_IN_YEAR`    varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '年周，格式：2019年第1周',
    PRIMARY KEY (`DATE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='日期维度信息';


-- ayykefu_ycbusi_ekf.cc_qc_result definition
CREATE TABLE `cc_qc_result`
(
    `QC_RESULT_ID`             varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '人工质检结果ID',
    `DATE_ID`                  int(11) DEFAULT NULL COMMENT '日期，格式:yyyymmdd',
    `ENT_ID`                   varchar(32) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '企业ID',
    `BUSI_ORDER_ID`            varchar(32) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '订购ID',
    `INSPECTOR_ID`             varchar(64) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '质检员用户ID',
    `INSPECTOR_NAME`           varchar(50) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '质检员姓名',
    `QC_TIME`                  varchar(19) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '质检执行时间，格式：yyyy-mm-dd HH:MM:SS',
    `AGENT_ID`                 varchar(64) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '坐席ID',
    `AGENT_NAME`               varchar(64) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '坐席姓名',
    `CALL_TYPE`                int(11) DEFAULT '1' COMMENT '通话类型，1 语音 2 全媒体',
    `SERIAL_ID`                varchar(64) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '通话ID或会话记录ID',
    `TASK_ID`                  varchar(64) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '任务ID',
    `TASK_NAME`                varchar(100) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '任务名称',
    `PASS_SCORE` double(10,2) DEFAULT NULL COMMENT '合格分数，冗余字段',
    `SCORE`                    decimal(10, 2)                    DEFAULT NULL COMMENT '得分',
    `PASS_FLAG`                int(11) DEFAULT NULL COMMENT '及格标志， 1 合格 0 不合格  ',
    `RESULT_DESC`              varchar(500) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '质检结果描述',
    `APPROVAL_STATE`           int(11) DEFAULT NULL COMMENT '申诉状态，0：未申诉，1：第一次，2：第二次，5：结案',
    `RESULT_CONTENT`           text COLLATE utf8mb4_bin COMMENT '质检项得分细则内容，用于导出细则结果描述',
    `BASE_SCORE`               decimal(10, 2)                    DEFAULT NULL COMMENT '基础得分',
    `BEGIN_TIME`               varchar(19) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '开始时间,保留',
    `END_TIME`                 varchar(19) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '结束时间,保留',
    `DURATION`                 varchar(30) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '持续时长,保留',
    `INSPECTOR_ACC`            varchar(30) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '质检员账号',
    `AGENT_ACC`                varchar(30) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '坐席账号',
    `EXAM_GROUP_ID`            varchar(64) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '质检分组ID，JSON',
    `ONE_VOTE_ITEM`            int(11) DEFAULT '0' COMMENT '命中一票否决的指标项数量',
    `AGENT_DEPT_CODE`          varchar(30) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '坐席所在部门编号',
    `INSPECTOR_DEPT_CODE`      varchar(30) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '质检员所在部门编号',
    `EVALUATE`                 varchar(12) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '评定结果1',
    `EVALUATE_EXPLAIN`         varchar(200) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '对评定结果的说明',
    `EVALUATE2`                varchar(12) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '评定结果2',
    `LINK_KM_JSON`             varchar(2000) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '质检关联知识点的JSON',
    `EVALUATE3`                varchar(20) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '评定结果3',
    `CACH_RESULT`              varchar(100) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '需辅导原因 字典QUALITY_COACHING_RESULT',
    `CACH_EXJOSN`              varchar(1000) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '需辅导的原因描述',
    `ALLROUND_ABILITY_SCORE_1` varchar(10) COLLATE utf8mb4_bin   DEFAULT '0' COMMENT '综合能力指标01得分',
    `ALLROUND_ABILITY_SCORE_2` varchar(10) COLLATE utf8mb4_bin   DEFAULT '0' COMMENT '综合能力指标02得分',
    `ALLROUND_ABILITY_SCORE_3` varchar(10) COLLATE utf8mb4_bin   DEFAULT '0' COMMENT '综合能力指标03得分',
    `ALLROUND_ABILITY_SCORE_4` varchar(10) COLLATE utf8mb4_bin   DEFAULT '0' COMMENT '综合能力指标04得分',
    `ALLROUND_ABILITY_SCORE_5` varchar(10) COLLATE utf8mb4_bin   DEFAULT '0' COMMENT '综合能力指标05得分',
    `CACH_STATE`               int(11) DEFAULT '0' COMMENT '0-无需辅导 1-待辅导 2-辅导结束',
    `CACH_BAKUP`               varchar(2000) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '辅导员填写的辅导结果备注信息',
    `CACH_ACC`                 varchar(30) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '辅导人员',
    `CACH_TIME`                varchar(19) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '辅导时间',
    PRIMARY KEY (`QC_RESULT_ID`) USING BTREE,
    KEY                        `IDX_CC_QC_RESULT_1` (`DATE_ID`,`ENT_ID`,`BUSI_ORDER_ID`) USING BTREE,
    KEY                        `IDX_CC_QC_RESULT_2` (`AGENT_ID`,`DATE_ID`) USING BTREE,
    KEY                        `IDX_CC_QC_RESULT_3` (`INSPECTOR_ACC`,`ENT_ID`,`BUSI_ORDER_ID`,`DATE_ID`) USING BTREE,
    KEY                        `IDX_CC_QC_RESULT_4` (`AGENT_ACC`,`ENT_ID`,`BUSI_ORDER_ID`,`DATE_ID`) USING BTREE,
    KEY                        `IDX_CC_QC_RESULT_5` (`APPROVAL_STATE`,`ENT_ID`,`BUSI_ORDER_ID`) USING BTREE,
    KEY                        `IDX_CC_QC_RESULT_SERIAL_ID` (`SERIAL_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='人工质检结果';


-- ayykefu_ycbusi_ekf.cc_qc_result_item definition

CREATE TABLE `cc_qc_result_item`
(
    `RESULT_ITEM_ID` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '人工质检结果项ID',
    `ENT_ID`         varchar(30) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '企业ID',
    `BUSI_ORDER_ID`  varchar(64) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '订购ID',
    `QC_RESULT_ID`   varchar(64) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '人工质检结果ID',
    `ITEM_ID`        varchar(64) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '质检项ID',
    `VALID_STATE`    int(11) DEFAULT '0' COMMENT '有效标志，0，未选中，1：选中',
    `P_ITEM_ID`      varchar(64) COLLATE utf8mb4_bin  DEFAULT '2000' COMMENT '父节点ID',
    `ITEM_NAME`      varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '质检项名称',
    `SCORE`          decimal(10, 2)                   DEFAULT NULL COMMENT '得分',
    `ITEM_DESC`      varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '质检项描述',
    `INDEX_NUM`      int(11) DEFAULT '1' COMMENT '排序序号',
    `IS_ONE_VOTE`    varchar(10) COLLATE utf8mb4_bin  DEFAULT 'N' COMMENT '是否一票否决(冗余),sf_yn,默认为N',
    `BACKUP`         varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '质检时可以添加备注原因',
    `IS_RADIO`       varchar(5) COLLATE utf8mb4_bin   DEFAULT '0' COMMENT '是否是单选,0多选，1单选',
    `SCORE_DESC`     varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
    `MARK_NAME`      varchar(30) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '标记名称',
    `MARK_VALUE`     varchar(30) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '标记值',
    PRIMARY KEY (`RESULT_ITEM_ID`) USING BTREE,
    KEY              `IDX_CC_QC_RESULT_ITEM_1` (`ITEM_ID`) USING BTREE,
    KEY              `IDX_CC_QC_RESULT_ITEM_2` (`QC_RESULT_ID`,`ENT_ID`,`BUSI_ORDER_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='人工质检结果项明细';