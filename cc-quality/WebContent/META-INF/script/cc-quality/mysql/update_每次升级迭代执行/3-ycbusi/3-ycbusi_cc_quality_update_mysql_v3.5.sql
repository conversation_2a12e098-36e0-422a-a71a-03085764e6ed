-- 20250514 质检抽取规则表 新增 工单来源字段
ALTER TABLE CC_QC_EXTRACT ADD APPEAL_SOURCE  VARCHAR(20) COMMENT '申诉来源';

-- 20250514 质检抽取规则表 新增 工单处理方字段
ALTER TABLE CC_QC_EXTRACT ADD CREATE_TYPE  VARCHAR(20) COMMENT '工单处理方:1:人工，2:RPA机器人';

-- 20250515 删除服务开始时间
DELETE FROM cc_qc_column WHERE `ID` = '8';
-- 20250515 删除服务结束时间
DELETE FROM cc_qc_column WHERE `ID` = '9';
-- 20250515 删除服务时长
DELETE FROM cc_qc_column WHERE `ID` = '206';

-- 20250519 新增客户消息量
INSERT INTO cc_qc_column(ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM, BUSI_TYPE)
VALUES ('211', '客户消息量', 'int', 'CUST_MSG_NUM', '2', '', 213, '1');
-- 20250519 新增坐席回复量
INSERT INTO cc_qc_column(ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM, BUSI_TYPE)
VALUES ('212', '坐席回复量', 'int', 'AGENT_MSG_NUM', '2', '', 214, '1');

-- 20250519 新增呼叫方向
INSERT INTO cc_qc_column(ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM, BUSI_TYPE)
VALUES ('213', '呼叫方向', 'selectOne', 'CREATE_CAUSE', '1', '{"dataMars":"QcCommonDao.getDict(QC_CALL_TYPE)"}', 215, '1');

-- 20250625 抽取字段更新补充
-- 话务渠道更新：
-- 删除时段 主叫 被叫 呼叫结果 满意度 技能组 挂断类型
-- 来电地区 是否为新员工 计费开始时间 计费结束时间 呼叫创建原因 话机号码
DELETE FROM cc_qc_column WHERE `ID` = '10';
DELETE FROM cc_qc_column WHERE `ID` = '101';
DELETE FROM cc_qc_column WHERE `ID` = '102';
DELETE FROM cc_qc_column WHERE `ID` = '103';
DELETE FROM cc_qc_column WHERE `ID` = '104';
DELETE FROM cc_qc_column WHERE `ID` = '105';
DELETE FROM cc_qc_column WHERE `ID` = '106';
DELETE FROM cc_qc_column WHERE `ID` = '107';
DELETE FROM cc_qc_column WHERE `ID` = '108';
DELETE FROM cc_qc_column WHERE `ID` = '110';
DELETE FROM cc_qc_column WHERE `ID` = '2';
DELETE FROM cc_qc_column WHERE `ID` = '3';
DELETE FROM cc_qc_column WHERE `ID` = '5';
DELETE FROM cc_qc_column WHERE `ID` = '6';

-- 新媒体渠道更新：
-- 技能组 结束原因 创建原因 是否为新员工 小结
DELETE FROM cc_qc_column WHERE `ID` = '203';
DELETE FROM cc_qc_column WHERE `ID` = '204';
DELETE FROM cc_qc_column WHERE `ID` = '205';
DELETE FROM cc_qc_column WHERE `ID` = '209';
DELETE FROM cc_qc_column WHERE `ID` = '210';

-- 20250525 智能质检结果表 新增 渠道类型
ALTER TABLE CC_QC_CAPACITY ADD CALL_TYPE  VARCHAR(20) COMMENT '渠道类型 1:话务 2:新媒体 4:工单';
-- 20250525 历史数据进行更新
UPDATE CC_QC_CAPACITY cqc JOIN CC_QC_TASK_OBJ cqt ON cqc.ID = cqt.ZN_RESULT_ID SET cqc.CALL_TYPE = cqt.CHANNEL_TYPE;
-- 20250526 智能质检结果表 新增 坐席账号信息
ALTER TABLE CC_QC_CAPACITY ADD AGENT_ACC  VARCHAR(30) COMMENT '坐席账号';
-- 20250526 历史数据进行更新
UPDATE CC_QC_CAPACITY cqc JOIN CC_QC_TASK_OBJ cqt ON cqc.ID = cqt.ZN_RESULT_ID SET cqc.AGENT_ACC = cqt.AGENT_ACC;

-- 20250603  质检对象记录表新增OBJ_STATE字段，记录全局质检记录生命周期
ALTER TABLE CC_QC_TASK_OBJ ADD OBJ_STATE  VARCHAR(32) COMMENT '质检记录全局状态,1:智能质检中 2:人工质检中 3:员工确认 4:员工申诉 5:辅导中 6:质检完成';
-- 20250603  质检对象记录表新增满意度评价字段，对应质检列表中用户评价字段
ALTER TABLE CC_QC_TASK_OBJ ADD SATISF_ID  VARCHAR(32) COMMENT '满意度id,话务冗余字段';
ALTER TABLE CC_QC_TASK_OBJ ADD SATISF_NAME  VARCHAR(32) COMMENT '满意度名称,全媒体冗余字段';

-- 20250610 全媒体抽取字段 渠道类型修改字典 删除所属渠道
UPDATE cc_qc_column SET JSON = '{"code":"CX_CHANNEL_KEY"}' WHERE ID = '201'
DELETE FROM cc_qc_column WHERE `ID` = '208';

-- 20250610 CC_QC_TASK_OBJ表 OBJ_STATE历史数据精准补全
-- 1. 智能质检中（1）
UPDATE CC_QC_TASK_OBJ SET OBJ_STATE = '1' WHERE ZN_STATE IN ('1','2') AND (RG_RESULT_ID IS NULL OR RG_RESULT_ID = '') AND ZN_RESULT_ID IS NOT NULL AND ZN_RESULT_ID != '';

-- 2. 人工质检中（2）
UPDATE CC_QC_TASK_OBJ SET OBJ_STATE = '2' WHERE ZN_STATE = '4' AND RG_STATE IN ('1','2') AND RG_RESULT_ID IS NOT NULL AND RG_RESULT_ID != ''  ;

-- 3. 员工确认（3）：
UPDATE CC_QC_TASK_OBJ SET OBJ_STATE = '3' WHERE ZN_STATE = '4' AND RG_STATE  IN ('3','4')  AND (RECONSIDER_FLAG IS NULL OR RECONSIDER_FLAG = 0);

-- 4. 申诉中（4）
UPDATE CC_QC_TASK_OBJ obj
    JOIN CC_QC_RESULT res ON obj.RG_RESULT_ID = res.QC_RESULT_ID
    SET obj.OBJ_STATE = '4'
WHERE obj.ZN_STATE = '4' AND  obj.RG_STATE  IN ('3','4')
    AND obj.RECONSIDER_FLAG > 0 AND res.APPROVAL_STATE IN ('1','2');

-- 5. 辅导中（5）
UPDATE CC_QC_TASK_OBJ obj
    JOIN CC_QC_RESULT res ON obj.RG_RESULT_ID = res.QC_RESULT_ID
    SET obj.OBJ_STATE = '5'
WHERE obj.ZN_STATE = '4' AND  obj.RG_STATE  IN ('3','4')
and  res.CACH_STATE = '1' AND res.APPROVAL_STATE = '5';

-- 6. 质检完成（6）
UPDATE CC_QC_TASK_OBJ obj
LEFT JOIN CC_QC_RESULT res ON obj.RG_RESULT_ID = res.QC_RESULT_ID
SET obj.OBJ_STATE = '6'
WHERE obj.ZN_STATE = '4' AND  obj.RG_STATE  IN ('3','4')
  AND res.APPROVAL_STATE = '5';
  AND res.CACH_STATE != '1';

-- 20250620 智能质检结果表 新增 坐席姓名
ALTER TABLE CC_QC_CAPACITY ADD AGENT_NAME  VARCHAR(200) COMMENT '坐席姓名';
UPDATE CC_QC_CAPACITY cqc JOIN CC_QC_TASK_OBJ cqt ON cqc.ID = cqt.ZN_RESULT_ID SET cqc.AGENT_NAME = cqt.AGENT_NAME;

-- 20250624 修复智能质检结果数据
UPDATE ayykefu_ycbusi_ekf.CC_QC_TASK_OBJ obj
    JOIN ayykefu_ycbusi_ekf.cc_qc_capacity cap ON obj.ZN_RESULT_ID = cap.ID
    SET obj.ZN_QC_SCORE = cap.SCORE
WHERE  obj.ZN_STATE = '4' and  obj.ZN_QC_SCORE is  null
--  智能质检结果新增命中话术字段
ALTER TABLE CC_QC_DETAILS ADD COLUMN HIGHLIGHT_SENTENCE VARCHAR(2000) COMMENT '多个关键词或话术使用|进行分隔';

-- 20250721 通话时长类型更新
UPDATE cc_qc_column SET COLUMN_TYPE = 'notLessThan' WHERE ID ='4';
UPDATE cc_qc_column SET JSON = '{"unit":"秒"}' WHERE ID ='4';

-- 20250721 抽取规则详情表 符号字段扩容
alter table cc_qc_extract_column modify COLUMN SYMBOL1 varchar(20);
alter table cc_qc_extract_column modify COLUMN SYMBOL2 varchar(20);
-- 20250721 抽取规则表 申诉来源扩容
alter table cc_qc_extract modify COLUMN APPEAL_SOURCE varchar(50);

-- 20250723 新增交互次数字段
INSERT INTO cc_qc_column(ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM, BUSI_TYPE)
VALUES ('214', '交互次数', 'notLessThan', 'INTERACTION_NUM', '2', '{"unit":"次"}', 215, '1');
-- 到时记得升级 cc-statgw、cc-report
ALTER TABLE C_PF_MEDIA_RECORD_EX ADD INTERACTION_NUM int DEFAULT 0 COMMENT '本通会话过程中,坐席与客户的交互次数';

-- 20250724 为CC_QC_EXTRACT表增加抽检周期字段
ALTER TABLE CC_QC_EXTRACT ADD INSPECT_PERIOD VARCHAR(500) COMMENT '抽检周期配置，JSON格式，如[{"name":"早上","start":"08:30","end":"12:00"},{"name":"下午","start":"12:00","end":"18:00"},{"name":"晚上","start":"18:00","end":"22:00"}]';

-- 20250724 为CC_QC_EXTRACT表增加每月每人最少条数字段
ALTER TABLE CC_QC_EXTRACT ADD MIN_MONTHLY_COUNT INT DEFAULT NULL COMMENT '每月每人不少于条数';

-- 20250804 删除客户消息量与坐席回复量字段
DELETE FROM cc_qc_column WHERE `ID` = '211';
DELETE FROM cc_qc_column WHERE `ID` = '212';

-- 20250826 SQL性能优化：为qcProblemStatByAi方法添加索引
-- 为cc_qc_capacity表添加复合索引，优化按时间范围和坐席名称查询
ALTER TABLE cc_qc_capacity ADD INDEX idx_qc_time_agent (QC_TIME, AGENT_NAME);

-- 为cc_qc_details表添加复合索引，优化子查询性能
ALTER TABLE cc_qc_details ADD INDEX idx_capacity_score (CAPACITY_ID, SCORE);