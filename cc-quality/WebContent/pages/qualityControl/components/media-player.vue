<template>
    <div>
        <div style="height: 20px;display: flex;align-items: center;gap: 8px;margin-bottom: 8px;">
            <el-tag size="small">{{ getI18nValue('通话号码:  ') + mediaMsg.CALLED }}</el-tag>
            <el-tag size="small">{{ getI18nValue('通话时间:  ') + mediaMsg.BILL_BEGIN_TIME }}</el-tag>
        </div>
        <div class="card-meida">
            <div :id="'wave-timeline' + serialId"></div>
            <div :id="'waveform' + serialId" class="mr-t8"></div>
        </div>
        <div class="card-opt">
            <div class="multiple">
                <el-select v-model="multiple" size="small" class="mr-r8">
                    <el-option value="0.5" label="0.5x"></el-option>
                    <el-option value="0.75" label="0.75x"></el-option>
                    <el-option value="1.0" label="1.0x"></el-option>
                    <el-option value="1.25" label="1.25x"></el-option>
                    <el-option value="1.5" label="1.5x"></el-option>
                    <el-option value="2.0" label="2.0x"></el-option>
                </el-select>
            </div>
            <div class="controTool">
                <el-tooltip class="item" effect="dark" :content="getI18nValue('后退')" placement="bottom">
                    <img src="/cc-quality/static/images/prev.png" alt="" class="mr-r16 optBtn" @click="mediaBack" />
                </el-tooltip>
                <el-tooltip class="item" effect="dark" :content="getI18nValue('播放/暂停')" placement="bottom">
                    <img :src="NowStatus == 'pause'
                        ? '/cc-quality/static/images/pause.png'
                        : '/cc-quality/static/images/play.png'
                        " alt="" class="mr-r16 optBtn" @click="mediaPlayPause" />
                </el-tooltip>
                <el-tooltip class="item" effect="dark" :content="getI18nValue('前进')" placement="bottom">
                    <img src="/cc-quality/static/images/next.png" alt="" class="optBtn" @click="mediaFoward" />
                </el-tooltip>
            </div>
            <div class="controOpt">
                <el-button size="small" type="primary" plain icon="my-icon-voice" class="mr-r16"
                    style="border-radius: 4px" @click="mediaMute">{{
                        isMute ? getI18nValue("已静音") : getI18nValue("静音")
                    }}</el-button>
                <el-button size="small" type="primary" plain icon="my-icon-download" style="border-radius: 4px"
                    @click="mediaDownLoad">{{ getI18nValue("下载录音") }}</el-button>
            </div>
        </div>
    </div>
</template>
<script>
module.exports = {
    name: 'media-player',
    props: {
        serialId: {
            type: String,
            default: ''
        },
        phone: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            multiple: '1.0',
            isMute: false,
            NowStatus: 'pause',
            ischeck: false,
            mediaUrl: '',
            wavesurfer: null,
            mediaMsg: {}
        }
    },
    watch:{
        multiple: {
            handler(newV) {
                if (this.wavesurfer) {
                this.wavesurfer.setPlaybackRate(newV)
                }
            },
        },
    },
    mounted() {
        this.getRecordFile()
    },
    methods: {
        getRecordFile() {
            let data = {
                serialId: this.serialId || "",
            };
            yq.remoteCall("/cc-base/servlet/call?action=getRecordFile", data).then(
                (res) => {
                    if (res.state == 1) {
                        this.mediaMsg = res.data;
                        this.mediaUrl = res.url + "&oper=download";
                        this.initWaver()
                    }
                }
            );
        },
        initWaver() {
            this.wavesurfer = WaveSurfer.create({
                container: "#waveform" + this.serialId,
                backgroundColor: "#f2f6fc",
                waveColor: "#9BBBEB",
                progressColor: "#0033FF",
                height: 20,
                barWidth: 1,
                hideScrollbar: true,
                barGap: 1,
                barHeight: 5,
                plugins: [
                    WaveSurfer.timeline.create({
                        container: "#wave-timeline" + this.serialId,
                    }),
                    WaveSurfer.regions.create({}), // 开启标注区
                    WaveSurfer.markers.create({}),
                ],
            });
            this.wavesurfer.on("ready", (res) => {
                console.log('audio-ready')
                // this.wavesurfer.playPause();
            });
            this.wavesurfer.on("pause", () => {
                this.NowStatus = "pause";
            });
            this.wavesurfer.on("play", () => {
                this.NowStatus = "play";
            });
            this.wavesurfer.load(this.mediaUrl);
        },
        mediaBack() {
            this.wavesurfer.skipBackward();
        },
        mediaFoward() {
            this.wavesurfer.skipForward();
        },
        mediaPlayPause() {
            this.wavesurfer.playPause();
        },
        mediaMute() {
            this.wavesurfer.setMute(!this.isMute);
            this.isMute = !this.isMute;
        },
        mediaDownLoad() {
            if (yq.isNull(this.mediaUrl)) {
                return;
            }
            location.href = this.mediaUrl;
        },
    }
}
</script>
<style scoped>
.card-meida {
    padding-bottom: 8px;
    border: 1px solid #f2f4f7;
    position: relative;
}

.card-opt {
    padding: 8px;
    border: 1px solid #f2f4f7;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
}

.card-opt .multiple {
    text-align: left;
}

.card-opt .controTool {
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-opt .controOpt {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.optBtn {
    text-align: right;
    cursor: pointer;
    transition: all 0.5s;
}

.optBtn:first-child {
    width: 24px;
    height: 24px;
}

.optBtn:nth-child(2) {
    width: 36px;
    height: 36px;
}

.optBtn:nth-child(3) {
    width: 24px;
    height: 24px;
}

.optBtn:hover {
    transform: scale(1.2);
}
</style>